"""
优先级自动分配函数使用示例

本文件展示了 assign_pattern_priority 数据库函数在不同场景下的使用方法，
包括在AWS服务同步、手动模式添加、批量处理等场景中的具体应用。
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

# 假设的导入（实际使用时需要根据项目结构调整）
from storage.rds_client import RDSClient
from processors.priority_assignment_helper import PriorityAssignmentHelper
from utils.structured_logger import StructuredLogger

logger = logging.getLogger(__name__)


class PriorityAssignmentExamples:
    """优先级分配使用示例类"""
    
    def __init__(self, db_client: RDSClient):
        self.db_client = db_client
        self.priority_helper = PriorityAssignmentHelper(db_client)
        self.logger = StructuredLogger("priority_examples")
    
    def example_1_aws_service_sync_integration(self):
        """
        示例1：AWS服务同步系统中的集成使用
        
        当AWS服务同步系统发现新服务时，自动生成正则模式并分配优先级
        """
        print("\n=== 示例1：AWS服务同步系统集成 ===")
        
        # 模拟新同步的服务信息
        new_service = {
            'id': 123,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'base_name': 'Amazon Elastic Compute Cloud',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        }
        
        # 生成不同类型的正则模式
        patterns_to_generate = [
            {
                'pattern_name': f"{new_service['service_code'].upper()}_FULL_STANDARD",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'Amazon\s+Elastic\s+Compute\s+Cloud\s*\(EC2\)',
                'related_service_id': new_service['id'],
                'is_compound': False,
                'description': '标准全称模式'
            },
            {
                'pattern_name': f"{new_service['service_code'].upper()}_FULL_COMPOUND",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'Amazon\s+Elastic\s+Compute\s+Cloud\s*\(EC2\)\s*((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))',
                'related_service_id': new_service['id'],
                'is_compound': True,
                'suffix_group': 'instance',
                'description': '全称复合模式（带实例后缀）'
            },
            {
                'pattern_name': f"{new_service['service_code'].upper()}_SHORT_STANDARD",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'Amazon\s+EC2\b',
                'related_service_id': new_service['id'],
                'is_compound': False,
                'description': '标准简称模式'
            },
            {
                'pattern_name': f"{new_service['service_code'].upper()}_ABBREV_STANDARD",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'(?<![:_-])\bEC2\b(?![:_-])',
                'related_service_id': new_service['id'],
                'is_compound': False,
                'description': '标准缩写模式'
            }
        ]
        
        # 批量分配优先级
        patterns_with_priority = self.priority_helper.assign_batch_pattern_priorities(patterns_to_generate)
        
        # 显示结果
        for pattern in patterns_with_priority:
            computed_priority = (11 - pattern['priority_tier']) * 10 + pattern['priority_value']
            print(f"模式: {pattern['pattern_name']}")
            print(f"  描述: {pattern['description']}")
            print(f"  优先级层级: {pattern['priority_tier']}")
            print(f"  层级内值: {pattern['priority_value']}")
            print(f"  计算优先级: {computed_priority}")
            print(f"  是否复合: {pattern.get('is_compound', False)}")
            print()
        
        return patterns_with_priority
    
    def example_2_manual_pattern_addition(self):
        """
        示例2：手动添加新正则模式时的使用
        
        管理员通过管理界面或SQL手动添加新的正则模式
        """
        print("\n=== 示例2：手动添加正则模式 ===")
        
        # 手动添加的新模式
        new_pattern = {
            'pattern_name': 'LAMBDA_CUSTOM_PATTERN',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'AWS\s+Lambda\s+function',
            'related_service_id': 456,  # Lambda服务的ID
            'is_compound': True,
            'suffix_group': 'function'
        }
        
        # 分配优先级
        priority_info = self.priority_helper.assign_single_pattern_priority(
            pattern_name=new_pattern['pattern_name'],
            pattern_type=new_pattern['pattern_type'],
            regex_string=new_pattern['regex_string'],
            related_service_id=new_pattern['related_service_id'],
            is_compound=new_pattern['is_compound'],
            suffix_group=new_pattern['suffix_group']
        )
        
        # 合并信息
        complete_pattern = {**new_pattern, **priority_info}
        
        print(f"新添加的模式: {complete_pattern['pattern_name']}")
        print(f"优先级层级: {complete_pattern['priority_tier']}")
        print(f"层级内值: {complete_pattern['priority_value']}")
        print(f"计算优先级: {(11 - complete_pattern['priority_tier']) * 10 + complete_pattern['priority_value']}")
        
        # 模拟插入数据库的SQL
        insert_sql = """
        INSERT INTO regex_patterns (
            pattern_name, pattern_type, regex_string, related_service_id,
            is_compound_pattern, compound_suffix_group,
            priority_tier, priority_value, is_active
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        insert_params = (
            complete_pattern['pattern_name'],
            complete_pattern['pattern_type'],
            complete_pattern['regex_string'],
            complete_pattern['related_service_id'],
            complete_pattern['is_compound'],
            complete_pattern['suffix_group'],
            complete_pattern['priority_tier'],
            complete_pattern['priority_value'],
            True
        )
        
        print(f"\n插入SQL: {insert_sql}")
        print(f"参数: {insert_params}")
        
        return complete_pattern
    
    def example_3_batch_priority_recalculation(self):
        """
        示例3：批量重新计算现有模式的优先级
        
        系统维护时，重新计算所有或特定服务的正则模式优先级
        """
        print("\n=== 示例3：批量优先级重新计算 ===")
        
        # 模拟从数据库查询现有模式
        existing_patterns = [
            {
                'id': 1,
                'pattern_name': 'S3_FULL_STANDARD',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'Amazon\s+Simple\s+Storage\s+Service\s*\(S3\)',
                'related_service_id': 789,
                'is_compound': False,
                'current_priority_tier': 5,  # 当前的优先级
                'current_priority_value': 3
            },
            {
                'id': 2,
                'pattern_name': 'S3_BUCKET_COMPOUND',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': r'Amazon\s+S3\s+(bucket|buckets)',
                'related_service_id': 789,
                'is_compound': True,
                'suffix_group': 'bucket',
                'current_priority_tier': 6,
                'current_priority_value': 5
            }
        ]
        
        print("重新计算优先级...")
        
        for pattern in existing_patterns:
            # 重新计算优先级
            new_priority = self.priority_helper.assign_single_pattern_priority(
                pattern_name=pattern['pattern_name'],
                pattern_type=pattern['pattern_type'],
                regex_string=pattern['regex_string'],
                related_service_id=pattern['related_service_id'],
                is_compound=pattern['is_compound'],
                suffix_group=pattern.get('suffix_group')
            )
            
            # 比较新旧优先级
            old_computed = (11 - pattern['current_priority_tier']) * 10 + pattern['current_priority_value']
            new_computed = (11 - new_priority['priority_tier']) * 10 + new_priority['priority_value']
            
            print(f"\n模式: {pattern['pattern_name']}")
            print(f"  旧优先级: 层级{pattern['current_priority_tier']}, 值{pattern['current_priority_value']} (计算值: {old_computed})")
            print(f"  新优先级: 层级{new_priority['priority_tier']}, 值{new_priority['priority_value']} (计算值: {new_computed})")
            print(f"  是否需要更新: {'是' if old_computed != new_computed else '否'}")
            
            # 模拟更新SQL
            if old_computed != new_computed:
                update_sql = """
                UPDATE regex_patterns 
                SET priority_tier = %s, priority_value = %s, updated_at = NOW()
                WHERE id = %s
                """
                print(f"  更新SQL: {update_sql}")
                print(f"  参数: ({new_priority['priority_tier']}, {new_priority['priority_value']}, {pattern['id']})")
    
    def example_4_direct_sql_usage(self):
        """
        示例4：直接在SQL中使用函数
        
        展示如何在SQL查询中直接调用 assign_pattern_priority 函数
        """
        print("\n=== 示例4：直接SQL使用 ===")
        
        # SQL示例1：插入新模式时自动分配优先级
        sql_insert_example = """
        INSERT INTO regex_patterns (
            pattern_name, pattern_type, regex_string, related_service_id,
            is_compound_pattern, compound_suffix_group,
            priority_tier, priority_value, is_active
        )
        SELECT 
            'RDS_MYSQL_COMPOUND',
            'SERVICE_NAME',
            'Amazon\\s+RDS\\s+for\\s+MySQL',
            (SELECT id FROM service_names WHERE service_code = 'rds'),
            TRUE,
            'mysql',
            tier,
            value,
            TRUE
        FROM assign_pattern_priority(
            'RDS_MYSQL_COMPOUND',
            'SERVICE_NAME',
            'Amazon\\s+RDS\\s+for\\s+MySQL',
            (SELECT id FROM service_names WHERE service_code = 'rds'),
            TRUE,
            'mysql'
        );
        """
        
        # SQL示例2：批量更新现有模式的优先级
        sql_update_example = """
        UPDATE regex_patterns 
        SET (priority_tier, priority_value) = (
            SELECT tier, value 
            FROM assign_pattern_priority(
                pattern_name,
                pattern_type,
                regex_string,
                related_service_id,
                is_compound_pattern,
                compound_suffix_group
            )
        )
        WHERE is_active = TRUE 
        AND related_service_id = (SELECT id FROM service_names WHERE service_code = 'ec2');
        """
        
        # SQL示例3：查询优先级分配结果
        sql_query_example = """
        SELECT 
            pattern_name,
            current_tier.priority_tier as current_tier,
            current_tier.priority_value as current_value,
            new_priority.tier as recommended_tier,
            new_priority.value as recommended_value,
            CASE 
                WHEN current_tier.priority_tier != new_priority.tier 
                  OR current_tier.priority_value != new_priority.value 
                THEN 'NEEDS_UPDATE'
                ELSE 'UP_TO_DATE'
            END as status
        FROM regex_patterns current_tier
        CROSS JOIN LATERAL assign_pattern_priority(
            current_tier.pattern_name,
            current_tier.pattern_type,
            current_tier.regex_string,
            current_tier.related_service_id,
            current_tier.is_compound_pattern,
            current_tier.compound_suffix_group
        ) as new_priority
        WHERE current_tier.is_active = TRUE;
        """
        
        print("SQL示例1 - 插入时自动分配优先级:")
        print(sql_insert_example)
        print("\nSQL示例2 - 批量更新优先级:")
        print(sql_update_example)
        print("\nSQL示例3 - 查询优先级分配状态:")
        print(sql_query_example)


def main():
    """主函数 - 运行所有示例"""
    print("=== assign_pattern_priority 函数使用示例 ===")
    
    # 注意：这里使用模拟的数据库客户端
    # 实际使用时需要创建真实的数据库连接
    db_client = None  # RDSClient(connection_params)
    
    if db_client:
        examples = PriorityAssignmentExamples(db_client)
        
        # 运行所有示例
        examples.example_1_aws_service_sync_integration()
        examples.example_2_manual_pattern_addition()
        examples.example_3_batch_priority_recalculation()
        examples.example_4_direct_sql_usage()
    else:
        print("注意：这是示例代码，需要真实的数据库连接才能运行")
        print("请参考代码中的注释了解具体使用方法")


if __name__ == "__main__":
    main()
