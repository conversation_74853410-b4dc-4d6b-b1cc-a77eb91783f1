---
type: "manual"
---

# AWS服务同步系统开发指南

## 核心原则

**🎯 功能精简原则**: AWS服务同步系统的所有子功能开发都应该**只实现功能本身**，不要过度开发增加其他功能，如：
- ❌ 不要添加复杂的监控和性能统计功能
- ❌ 不要添加详细的报告生成和分析功能  
- ❌ 不要添加复杂的测试套件和验证工具
- ❌ 不要添加过度的日志记录和指标收集
- ✅ 只实现核心业务功能和基本错误处理
- ✅ 使用简单的日志记录（基本的成功/失败状态）
- ✅ 保持代码简洁和可维护性

## 系统概述

AWS服务同步系统是一个基于AWS Lambda的解决方案，从官方网页和PDF文档中抓取、解析和同步AWS中国区服务名称数据。

**核心功能模块**:
1. **网页爬虫模块** - 从AWS中国区官网抓取服务名称
2. **PDF解析模块** - 解析S3存储的PDF文档
3. **数据处理模块** - 合并和标准化数据
4. **数据库存储模块** - 存储到PostgreSQL数据库
5. **正则表达式模式生成** - 为服务名称生成匹配模式

## 核心架构

### 模块结构
```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端
└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

## 数据获取模块

### Web Scraper模块
- **目标URL**: https://www.amazonaws.cn/en/about-aws/regional-product-services/
- **提取内容**: "Services Offered"字段的值作为权威服务全称
- **基本验证**: 检查数据完整性
- **简单错误处理**: 网络访问失败时记录错误

### PDF Parser模块
- **数据源**: S3存储的AWS官方PDF文档
- **解析内容**: 三列数据（AWS offering, Long name, Short name）
- **数据清理**: 标准化服务名称格式
- **基本验证**: 检查解析结果

## 数据处理模块

### 数据合并和标准化
- **匹配逻辑**: 网页数据与PDF数据的智能匹配
- **字段生成**: 
  - `base_name`: 从`authoritative_full_name`移除括号内容
  - `service_code`: 从`internal_name`生成小写代码
- **去重处理**: 基于`authoritative_full_name`去重

## 数据库集成

### service_names表结构

```sql
CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    authoritative_full_name VARCHAR(255) NOT NULL UNIQUE,  -- 业务主键
    base_name VARCHAR(255) NOT NULL,                       -- 规范化名称
    internal_name VARCHAR(255),                            -- PDF内部名称
    full_name_en VARCHAR(255) NOT NULL,                    -- 英文全称
    short_name_en VARCHAR(100) NOT NULL,                   -- 英文简称
    service_code VARCHAR(50),                              -- 服务代码
    source rule_source NOT NULL DEFAULT 'manual',         -- 数据来源
    is_active BOOLEAN NOT NULL DEFAULT TRUE,               -- 是否启用
    last_synced_at TIMESTAMPTZ,                           -- 同步时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### UPSERT操作

```python
def upsert_service(service_data):
    """使用authoritative_full_name作为业务主键进行UPSERT操作"""
    sql = """
    INSERT INTO service_names 
        (authoritative_full_name, base_name, internal_name, full_name_en, 
         short_name_en, service_code, source, last_synced_at)
    VALUES 
        (%(auth_name)s, %(base_name)s, %(internal_name)s, %(full_name)s, 
         %(short_name)s, %(service_code)s, %(source)s, NOW())
    ON CONFLICT (authoritative_full_name) 
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.full_name_en,
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    """
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(sql, service_data)
```


### 模式生成策略

**⚠️ 重要**: 模式生成策略已完全重构。请参考 **[AWS服务模式生成系统设计规范](aws-service-pattern-generation-guide.md)** 获取完整的实现指导。

#### 核心改进

1. **8种模式类型**：从4种基本模式扩展到8种精确模式
2. **分层优先级**：使用90-130的精确优先级控制系统
3. **边界保护机制**：避免ARN、URL等上下文中的误匹配
4. **复合词智能处理**：支持复杂后缀的精确捕获和保留
5. **特殊服务支持**：针对Aurora、Health、RDS等服务的专门处理

#### 快速参考

```python
# 使用新的模式生成系统
from processors.regex_pattern_generator import generate_comprehensive_service_patterns

def sync_patterns_for_services(services_data):
    """同步服务的正则表达式模式"""
    all_patterns = []
    
    for service_data in services_data:
        # 生成完整的模式集合（8种核心类型 + 特殊变体）
        service_patterns = generate_comprehensive_service_patterns(service_data)
        all_patterns.extend(service_patterns)
    
    # 批量插入到数据库
    validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)
    
    logger.info(f"Generated {len(all_patterns)} patterns, validated {validated_count}")
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")
    
    return len(all_patterns)
```

#### 模式示例

新系统生成的模式示例（以EC2为例）：

```python
# 1. 全称复合后缀模式 (优先级: 120)
"Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))"

# 2. 全称标准模式 (优先级: 115) - 带边界保护
"(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)(?<!https?://[^\\s]*)\\bAmazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\b(?![:_-])(?![^\\s]*\\.[a-z]{2,4})"

# 3. 简称复合后缀模式 (优先级: 110)
"Amazon\\s+EC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))"

# 4. 缩写标准模式 (优先级: 95) - 带边界保护
"(?<![:_-])\\bEC2\\b(?![:_-])"
```

详细的实现指导、特殊服务处理和性能优化策略请参考完整的设计规范文档。

## 配置管理

### AWS Secrets Manager集成

```python
class ConfigManager:
    def __init__(self):
        self.secrets_client = boto3.client('secretsmanager', region_name='cn-northwest-1')
        self.secret_arn = os.environ.get('SECRETS_MANAGER_ARN')
        self._config_cache = None
    
    def get_config(self):
        """从Secrets Manager获取配置信息"""
        if self._config_cache is None:
            try:
                response = self.secrets_client.get_secret_value(SecretId=self.secret_arn)
                self._config_cache = json.loads(response['SecretString'])
            except Exception as e:
                logger.error(f"Failed to get config: {e}")
                raise
        return self._config_cache
```

### 配置结构
```json
{
  "host": "数据库主机",
  "port": "5432",
  "username": "数据库用户名",
  "password": "数据库密码",
  "database": "数据库名",
  "snpdf": "s3://bucket/path/to/pdf"
}
```

## 基本错误处理

### 重试机制

```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type((ConnectionError, TimeoutError))
)
def fetch_data_with_retry(url):
    """带基本重试机制的数据获取"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return response
    except requests.RequestException as e:
        logger.error(f"Failed to fetch data: {e}")
        raise
```

### 基本日志记录

```python
import logging

logger = logging.getLogger(__name__)

def log_sync_result(success, processed_count, error_message=None):
    """记录同步结果的基本信息"""
    if success:
        logger.info(f"同步成功，处理了 {processed_count} 个服务")
    else:
        logger.error(f"同步失败: {error_message}")
```

## 开发指导原则

### 1. 保持简洁
- 每个模块只实现其核心功能
- 避免添加复杂的统计和分析功能
- 使用简单直接的实现方式

### 2. 基本错误处理
- 实现必要的重试机制
- 记录关键错误信息
- 不要过度设计错误处理逻辑

### 3. 最小化依赖
- 只使用必要的第三方库
- 避免引入复杂的框架
- 保持代码的可维护性

### 4. 数据验证
- 实现基本的数据完整性检查
- 验证必要字段的存在
- 不要过度验证非关键数据

## Lambda函数入口

```python
def lambda_handler(event, context):
    """Lambda函数入口 - 保持简洁"""
    try:
        logger.info("开始执行服务同步")
        
        # 1. 初始化配置
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # 2. 抓取网页数据
        web_scraper = WebScraper()
        web_services = web_scraper.scrape_services()
        
        # 3. 解析PDF数据
        pdf_parser = PDFParser(config)
        pdf_services = pdf_parser.parse_pdf()
        
        # 4. 处理和合并数据
        data_processor = DataProcessor()
        merged_services = data_processor.process_services(web_services, pdf_services)
        
        # 5. 存储到数据库
        rds_client = RDSClient(config)
        stored_count = rds_client.store_services(merged_services)
        
        # 6. 生成正则表达式模式
        from processors.regex_pattern_generator import batch_generate_all_patterns, batch_insert_patterns_optimized
        
        # 生成完整的模式集合
        all_patterns = batch_generate_all_patterns(merged_services)
        
        # 批量插入到数据库
        validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)
        
        logger.info(f"Pattern generation completed: {len(all_patterns)} total, {validated_count} validated")
        if conflicts:
            logger.warning(f"Detected {len(conflicts)} pattern conflicts")
        
        logger.info(f"同步完成，处理了 {stored_count} 个服务")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'processed_count': stored_count
            })
        }
        
    except Exception as e:
        logger.error(f"同步失败: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e)
            })
        }
```

## 总结

AWS服务同步系统应该专注于核心功能实现：
1. **数据抓取** - 从网页和PDF获取服务名称
2. **数据处理** - 合并、标准化和去重
3. **数据存储** - 存储到数据库
4. **模式生成** - 生成正则表达式匹配模式

避免过度开发监控、统计、测试等附加功能，保持系统的简洁性和可维护性。