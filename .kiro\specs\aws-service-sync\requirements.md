# AWS服务同步系统需求文档

## 介绍

AWS服务同步系统是批量邮件翻译系统的核心组件，旨在定期从官方网页和PDF文档中获取、整理和同步AWS中国区的服务名称信息。该系统实现"逻辑外化"设计理念，将硬编码的服务名称列表转换为数据库驱动的动态管理系统，确保翻译系统中的服务名称数据始终保持最新和准确。

**🎯 核心设计原则**: 遵循**功能精简原则**，只实现核心业务功能和基本错误处理，保持代码简洁和可维护性。

## 需求

### 需求1：EventBridge定时调度系统

**用户故事：** 作为系统管理员，我希望系统能够按照预定计划自动执行同步任务，确保数据的时效性和系统的自动化运行。

#### 验收标准

1. WHEN 系统部署完成 THEN 系统 SHALL 通过EventBridge Scheduler按配置的调度规则自动触发Lambda函数执行
2. WHEN 配置调度规则 THEN 系统 SHALL 支持灵活的调度表达式（cron格式），包括每日、每周、每月等不同频率
3. WHEN 同步任务运行 THEN 系统 SHALL 防止重复执行和并发冲突，确保同一时间只有一个同步任务运行
4. IF 同步任务失败 THEN 系统 SHALL 根据重试策略自动重试，最多重试3次
5. WHEN 同步任务完成 THEN 系统 SHALL 更新下次执行时间并记录执行历史到CloudWatch日志
6. WHEN 调度器触发 THEN 系统 SHALL 支持手动触发和定时触发两种模式，便于测试和紧急同步

### 需求2：多数据源网页抓取系统

**用户故事：** 作为系统管理员，我希望能够自动抓取AWS中国区官方网页的服务信息，以获取最权威的服务名称数据。

#### 验收标准

1. WHEN 系统执行同步任务 THEN 系统 SHALL 访问 https://www.amazonaws.cn/en/about-aws/regional-product-services/ 页面
2. WHEN 访问官方页面 THEN 系统 SHALL 使用BeautifulSoup解析HTML，提取所有"Services Offered"字段的值作为服务的权威全称
3. WHEN 提取服务名称 THEN 系统 SHALL 验证数据完整性，确保提取的服务数量大于0且与历史数据相比变化幅度在合理范围内（±30%）
4. IF 网页访问失败 THEN 系统 SHALL 实施重试机制（最多3次），记录详细错误日志并触发告警
5. WHEN 数据提取完成 THEN 系统 SHALL 将网页数据标记为'web_scrape'数据源，并记录提取统计信息
6. WHEN 处理网页数据 THEN 系统 SHALL 实施速率限制和User-Agent轮换，遵守网站的robots.txt规则

### 需求3：S3 PDF文档解析系统

**用户故事：** 作为系统管理员，我希望能够解析存储在S3中的AWS官方PDF文档，以获取服务的详细命名信息和简称。

#### 验收标准

1. WHEN 系统处理PDF文档 THEN 系统 SHALL 从AWS Secrets Manager获取S3路径配置，解析s3://bucket/key格式
2. WHEN 下载PDF文件 THEN 系统 SHALL 使用pdfplumber库解析所有三列数据：AWS offering (internal name)、Long name (First use)、Short name (Subsequent use)
3. WHEN 解析PDF数据 THEN 系统 SHALL 将所有'AWS'和'aws'字符串替换为'Amazon'进行标准化处理
4. WHEN 处理内部名称 THEN 系统 SHALL 清理"AWS offering (internal name)"字段中的特殊字符和空格，生成规范化的service_code
5. WHEN 数据预处理完成 THEN 系统 SHALL 临时存储处理后的PDF数据用于后续智能匹配
6. IF PDF文件损坏或无法读取 THEN 系统 SHALL 记录错误并继续使用现有数据，不中断同步流程
7. WHEN PDF解析完成 THEN 系统 SHALL 验证解析的记录数量并记录统计信息到结构化日志

### 需求4：智能数据匹配和处理系统

**用户故事：** 作为系统管理员，我希望系统能够智能匹配网页和PDF数据，生成准确的服务名称映射关系，并实现数据的标准化处理。

#### 验收标准

1. WHEN 执行匹配逻辑 THEN 系统 SHALL 使用网页抓取的"Services Offered"字段值作为匹配基准和业务主键
2. WHEN 进行智能匹配 THEN 系统 SHALL 使用高级匹配算法，包括精确匹配、模糊匹配和正则表达式匹配
3. WHEN 匹配成功 THEN 系统 SHALL 获取PDF中对应的"Short name (Subsequent use)"作为服务简称
4. IF 网页服务名称在PDF中未匹配到 THEN 系统 SHALL 将该服务的简称设置为与全称相同
5. WHEN 数据标准化 THEN 系统 SHALL 实施去重算法，确保同一服务不会重复存储
6. WHEN 匹配过程完成 THEN 系统 SHALL 记录匹配成功率和未匹配的服务列表到结构化日志

### 需求5：PostgreSQL v2架构数据存储系统

**用户故事：** 作为系统管理员，我希望同步的服务名称数据能够正确存储到PostgreSQL数据库的v2架构中，支持增量更新和字段职责分离。

#### 验收标准

1. WHEN 数据匹配完成 THEN 系统 SHALL 将网页"Services Offered"值存储为authoritative_full_name字段，作为同步操作的唯一业务键
2. WHEN 存储服务数据 THEN 系统 SHALL 将网页"Services Offered"值存储为full_name_en字段，与authoritative_full_name保持一致
3. WHEN 推导base_name THEN 系统 SHALL 通过移除authoritative_full_name中的括号缩写（使用正则表达式`r'\s*\([^)]*\)'`）生成用于翻译逻辑的规范化基础名称
4. WHEN 匹配成功 THEN 系统 SHALL 将PDF的"Short name (Subsequent use)"存储为short_name_en字段
5. IF PDF匹配失败 THEN 系统 SHALL 将short_name_en设置为与authoritative_full_name相同的值
6. WHEN 处理PDF数据 THEN 系统 SHALL 将"AWS offering (internal name)"存储为internal_name字段
7. WHEN 处理PDF数据 THEN 系统 SHALL 基于internal_name生成service_code字段（转换为小写并处理特殊字符）
8. WHEN 更新数据库 THEN 系统 SHALL 设置source字段为'web_scrape'标识数据来源
9. WHEN 执行存储操作 THEN 系统 SHALL 使用ON CONFLICT (authoritative_full_name) DO UPDATE实现UPSERT功能
10. WHEN 更新现有记录 THEN 系统 SHALL 更新last_synced_at时间戳并保持created_at不变
11. WHEN 字段职责分离 THEN 系统 SHALL 确保authoritative_full_name负责数据同步、base_name负责翻译逻辑状态跟踪、full_name_en提供首次提及替换值、short_name_en提供后续提及替换值

### 需求6：8种核心正则表达式模式自动生成系统

**用户故事：** 作为系统管理员，我希望同步过程能够基于服务名称自动生成和维护regex_patterns表中的8种核心正则表达式模式，确保翻译系统能够正确识别和处理服务名称。

#### 验收标准

1. WHEN 发现新服务或服务名称更新 THEN 系统 SHALL 基于服务名称自动生成8种核心正则表达式模式类型
2. WHEN 生成全称复合后缀模式 THEN 系统 SHALL 创建优先级120的模式，匹配完整服务名称加复杂后缀（如"Amazon EC2 instance"）
3. WHEN 生成全称标准模式 THEN 系统 SHALL 创建优先级115的模式，精确匹配完整服务名称，带边界保护机制
4. WHEN 生成简称复合后缀模式 THEN 系统 SHALL 创建优先级110的模式，匹配简称加复杂后缀
5. WHEN 生成简称标准模式 THEN 系统 SHALL 创建优先级105的模式，精确匹配服务简称，带边界保护
6. WHEN 生成缩写复合后缀模式 THEN 系统 SHALL 创建优先级100的模式，匹配纯缩写加后缀
7. WHEN 生成缩写标准模式 THEN 系统 SHALL 创建优先级95的模式，精确匹配纯缩写，带边界保护
8. WHEN 生成特殊变体模式 THEN 系统 SHALL 创建优先级125的模式，处理特殊服务变体（如Aurora PostgreSQL）
9. WHEN 生成上下文保护模式 THEN 系统 SHALL 创建优先级90的模式，在特定上下文中避免误匹配
10. WHEN 设置优先级 THEN 系统 SHALL 遵循"数值越大优先级越高"原则，确保精确匹配优先于模糊匹配
11. WHEN 生成复合模式 THEN 系统 SHALL 在notes字段中标记isCompoundWithSuffix: true和suffixGroup信息
12. WHEN 正则模式生成完成 THEN 系统 SHALL 验证模式的语法有效性并确保related_service_id正确关联

### 需求7：边界保护机制系统

**用户故事：** 作为系统管理员，我希望生成的正则表达式模式具备完善的边界保护机制，避免在ARN、URL、代码标识符等上下文中的误匹配。

#### 验收标准

1. WHEN 生成ARN保护模式 THEN 系统 SHALL 使用负向前瞻`(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)`避免ARN中的服务名匹配
2. WHEN 生成URL保护模式 THEN 系统 SHALL 使用`(?<!https?://[^\s]*)`和`(?![^\s]*\.[a-z]{2,4})`避免URL路径中的服务名误匹配
3. WHEN 生成代码保护模式 THEN 系统 SHALL 使用`(?<![:_-])`和`(?![:_-])`防止代码块中的服务名被错误识别
4. WHEN 处理复合词 THEN 系统 SHALL 支持复合词智能处理和后缀捕获，精确识别"基础服务名称 + 可变后缀"的模式
5. WHEN 处理特殊服务 THEN 系统 SHALL 为Aurora、Health、RDS等服务提供专门的处理策略
6. WHEN 生成上下文感知模式 THEN 系统 SHALL 支持CLI命令和配置文件上下文的智能识别

### 需求8：AWS Secrets Manager配置管理系统  ###

**用户故事：** 作为系统管理员，我希望系统能够安全地管理敏感配置信息，通过AWS Secrets Manager存储和获取数据库凭证和S3路径配置。

#### 验收标准

1. WHEN 管理敏感配置 THEN 系统 SHALL 使用AWS Secrets Manager存储数据库凭证（host、port、username、password、database）和S3路径配置
2. WHEN 获取配置 THEN 系统 SHALL 实现ConfigManager类，提供配置缓存机制（1小时TTL），减少API调用频率
3. WHEN 解析S3路径 THEN 系统 SHALL 自动解析s3://bucket/key格式的路径并验证有效性
4. WHEN 数据库连接 THEN 系统 SHALL 使用连接池管理数据库连接，支持重试和健康检查
5. WHEN 配置缓存过期 THEN 系统 SHALL 自动刷新缓存并重新获取最新配置信息

### 需求9：结构化日志和CloudWatch监控系统

**用户故事：** 作为系统管理员，我希望系统能够输出结构化的日志信息，便于通过CloudWatch监控同步任务的执行状态和性能指标。

#### 验收标准

1. WHEN 同步任务执行 THEN 系统 SHALL 记录结构化的JSON格式日志，包含时间戳、级别、组件、事件类型、指标和上下文信息
2. WHEN 记录日志 THEN 系统 SHALL 使用统一的日志格式和级别管理（INFO、WARN、ERROR），便于CloudWatch解析
3. WHEN 输出统计信息 THEN 系统 SHALL 记录处理数量、成功率、执行时间等关键指标到日志中
4. WHEN 发生异常 THEN 系统 SHALL 记录详细的错误信息和异常堆栈到结构化日志中
5. WHEN 记录执行状态 THEN 系统 SHALL 输出关键事件（sync_start、sync_progress、sync_complete、error_occurred）
6. WHEN 输出性能指标 THEN 系统 SHALL 记录资源使用情况（内存、CPU）和性能数据到日志中
7. WHEN 同步完成 THEN 系统 SHALL 输出数据同步统计信息的结构化日志，便于监控系统识别

### 需求10：分级错误处理和重试机制系统

**用户故事：** 作为系统管理员，我希望系统具备完善的错误处理、重试机制和恢复策略，确保系统的稳定性和可靠性。

#### 验收标准

1. WHEN 系统运行 THEN 系统 SHALL 提供分级的错误处理机制，包括网络错误、数据库错误、文件系统错误、解析错误、验证错误、超时错误和资源错误
2. WHEN 发生可恢复错误 THEN 系统 SHALL 根据错误类型应用相应的重试策略（网络请求3次、数据库操作5次、文件操作3次）
3. WHEN 实施重试 THEN 系统 SHALL 使用指数退避算法，基础延迟1-2秒，最大延迟60秒
4. WHEN 错误分类 THEN 系统 SHALL 根据错误严重程度（LOW、MEDIUM、HIGH、CRITICAL）采取不同的处理策略
5. WHEN 发生严重错误 THEN 系统 SHALL 记录详细的错误上下文和堆栈信息，便于故障排查
6. WHEN 资源管理 THEN 系统 SHALL 提供超时控制（5分钟）和资源监控，防止资源耗尽和长时间运行

### 需求11：数据质量保证和验证系统

**用户故事：** 作为系统管理员，我希望同步过程具有完整的数据质量检查和验证机制，确保数据的准确性和完整性。

#### 验收标准

1. WHEN 执行同步任务 THEN 系统 SHALL 验证所有必填字段的完整性（authoritative_full_name、base_name、full_name_en、short_name_en）
2. WHEN 发现数据异常 THEN 系统 SHALL 记录详细的错误日志并继续处理其他数据，不中断整个同步流程
3. WHEN 同步完成 THEN 系统 SHALL 生成同步报告，包含成功、失败和跳过的记录统计
4. IF 关键数据缺失 THEN 系统 SHALL 发送告警通知给管理员，通过CloudWatch日志记录
5. WHEN 数据验证失败 THEN 系统 SHALL 保持现有数据不变并记录验证失败原因
6. WHEN 检测数据一致性 THEN 系统 SHALL 验证外键关系和数据完整性约束

### 需求12：批量操作优化和性能管理系统   ###

**用户故事：** 作为系统管理员，我希望同步系统能够高效处理大量数据，确保同步操作的性能和稳定性。

#### 验收标准

1. WHEN 处理大量服务数据 THEN 系统 SHALL 使用psycopg2.extras.execute_values进行批量插入操作，提高数据库写入性能
2. WHEN 执行批量操作 THEN 系统 SHALL 使用数据库事务确保操作的原子性，支持回滚机制
3. WHEN 批量操作失败 THEN 系统 SHALL 回退到单条记录处理模式并记录失败原因
4. WHEN 处理大数据集 THEN 系统 SHALL 使用分页处理（每批100条记录）避免内存溢出
5. WHEN 执行数据库操作 THEN 系统 SHALL 使用连接池管理数据库连接，提高连接复用效率
6. WHEN 监控性能 THEN 系统 SHALL 记录处理吞吐量、执行时间和资源使用情况

### 需求13：正则表达式模式优化和维护系统   #####

**用户故事：** 作为系统管理员，我希望系统能够自动优化和维护正则表达式模式，确保模式的有效性和性能。

#### 验收标准

1. WHEN 生成正则模式 THEN 系统 SHALL 验证所有模式的语法正确性，将无效模式标记为'invalid'状态
2. WHEN 检测模式冲突 THEN 系统 SHALL 识别优先级冲突和重复模式，记录冲突详情
3. WHEN 分析模式性能 THEN 系统 SHALL 评估模式的匹配效率和资源消耗
4. WHEN 维护模式 THEN 系统 SHALL 支持模式的清理、归档和备份功能
5. WHEN 优化模式 THEN 系统 SHALL 提供模式优化建议和性能改进方案
6. WHEN 测试模式 THEN 系统 SHALL 使用样本数据验证模式的匹配准确性

### 需求14：系统集成和向后兼容性  ###

**用户故事：** 作为系统管理员，我希望新的同步系统能够与现有的翻译系统无缝集成，不影响现有功能的正常运行。

#### 验收标准

1. WHEN 同步系统部署 THEN 系统 SHALL 保持现有service_names表结构的兼容性，支持v2架构的字段扩展
2. WHEN 更新服务数据 THEN 系统 SHALL 确保现有翻译流程能够正常使用新数据，不破坏现有业务逻辑
3. WHEN 生成正则模式 THEN 系统 SHALL 遵循现有regex_patterns表的格式和约定，保持API兼容性
4. IF 发现兼容性问题 THEN 系统 SHALL 提供数据迁移工具和详细的迁移指南
5. WHEN 系统运行 THEN 系统 SHALL 支持逐步迁移而不中断现有服务，确保业务连续性
6. WHEN 数据更新 THEN 系统 SHALL 维护与translation_jobs表的关联关系，确保翻译历史的完整性

### 需求15：自动化部署和监控基础设施  ###

**用户故事：** 作为DevOps工程师，我希望有完整的自动化部署系统和监控配置，以便快速、安全地部署和管理AWS服务同步系统。

#### 验收标准

1. WHEN 需要部署系统 THEN 系统 SHALL 提供一键部署脚本，自动完成Lambda函数、IAM角色、EventBridge调度器的创建和配置
2. WHEN 执行部署 THEN 系统 SHALL 支持多环境部署（dev、test、prod），每个环境具有独立的配置和资源
3. WHEN 配置权限 THEN 系统 SHALL 自动创建最小权限的IAM角色和策略，遵循AWS安全最佳实践
4. WHEN 设置监控 THEN 系统 SHALL 自动配置8种CloudWatch告警（错误率、执行时间、调用次数、并发执行、限流、成功率、处理数量、内存使用）
5. WHEN 创建仪表板 THEN 系统 SHALL 自动创建CloudWatch仪表板，包含Lambda基础指标、同步任务指标、各步骤成功率、资源使用情况和错误日志
6. WHEN 配置通知 THEN 系统 SHALL 自动创建SNS主题和订阅，支持邮件告警通知
7. WHEN 执行测试 THEN 系统 SHALL 提供测试脚本，支持函数调用测试、日志分析、指标检查和健康状态评估
8. WHEN 需要更新 THEN 系统 SHALL 支持代码更新、配置变更和版本管理，确保零停机部署
9. WHEN 故障排查 THEN 系统 SHALL 提供详细的故障排查指导和常见问题解决方案
10. WHEN 成本优化 THEN 系统 SHALL 提供资源配置优化建议和成本监控指导

### 需求16：云上测试和验证支持   ###

**用户故事：** 作为开发人员，我希望有完整的云上测试指导和验证工具，以确保Lambda函数在AWS环境中的质量和可靠性。

#### 验收标准

1. WHEN 准备测试 THEN 系统 SHALL 提供EventBridge测试事件模板，用于在AWS控制台中手动触发Lambda函数
2. WHEN 执行云上测试 THEN 系统 SHALL 提供CloudWatch日志分析指导，验证Lambda函数执行结果
3. WHEN 验证功能 THEN 系统 SHALL 提供功能验证检查清单，确保所有核心功能正常工作
4. WHEN 性能监控 THEN 系统 SHALL 提供CloudWatch指标监控配置，跟踪执行时间和资源使用
5. WHEN 故障排查 THEN 系统 SHALL 提供详细的部署和运行故障排查指导文档
6. WHEN 环境验证 THEN 系统 SHALL 提供环境配置验证工具，检查IAM权限、网络连通性和服务可用性

### 需求17：数据迁移和历史管理系统  ###

**用户故事：** 作为系统管理员，我希望系统能够安全地处理数据迁移和历史数据管理，确保数据的完整性和可追溯性。

#### 验收标准

1. WHEN 执行数据迁移 THEN 系统 SHALL 提供从旧表结构到v2架构的安全迁移工具
2. WHEN 迁移数据 THEN 系统 SHALL 备份现有数据并验证迁移结果的完整性
3. WHEN 管理历史数据 THEN 系统 SHALL 记录所有同步操作的历史，包括时间戳、操作类型和结果
4. WHEN 数据回滚 THEN 系统 SHALL 支持数据回滚功能，在出现问题时恢复到之前的状态
5. WHEN 清理数据 THEN 系统 SHALL 提供数据清理工具，安全删除过期或无效的数据
6. WHEN 审计数据 THEN 系统 SHALL 提供数据审计功能，跟踪数据变更和访问记录

### 需求18：模式生命周期管理系统   ###

**用户故事：** 作为系统管理员，我希望系统能够完整管理正则表达式模式的生命周期，包括创建、验证、优化、维护和清理。

#### 验收标准

1. WHEN 创建新模式 THEN 系统 SHALL 自动生成完整的8种核心模式类型，确保覆盖所有使用场景
2. WHEN 验证模式 THEN 系统 SHALL 检查模式语法、性能和准确性，标记验证状态
3. WHEN 优化模式 THEN 系统 SHALL 分析模式使用情况，提供优化建议和性能改进
4. WHEN 维护模式 THEN 系统 SHALL 定期清理未使用的模式，归档历史模式
5. WHEN 备份模式 THEN 系统 SHALL 提供模式备份和恢复功能，确保数据安全
6. WHEN 导入导出模式 THEN 系统 SHALL 支持模式的导入导出，便于环境间迁移