---
type: "manual"
---

# AWS服务模式生成系统设计规范

## 概述

本规范定义了AWS服务同步系统中正则表达式模式生成的完整策略，基于实际项目需求和复杂场景分析，提供全面、精确、精简的模式生成方案。

## 核心设计原则

### 1. 分层优先级系统
- **优先级范围**: 90-120，数值越大优先级越高
- **层级划分**: 每10个数值为一个层级，确保精确控制
- **冲突避免**: 通过优先级和边界保护双重机制避免误匹配

### 2. 边界保护机制
- **ARN保护**: 使用负向前瞻避免ARN中的服务名匹配
- **URL保护**: 避免URL路径中的服务名误匹配
- **代码保护**: 防止代码块中的服务名被错误识别

### 3. 复合词智能处理
- **后缀捕获**: 支持复杂后缀的精确捕获和保留
- **变体识别**: 处理服务的多种表达形式
- **上下文感知**: 根据上下文选择合适的匹配策略

## 模式生成策略

### 核心模式类型（8种）

#### 1. 全称复合后缀模式 (优先级: 120)
**用途**: 匹配完整服务名称 + 复杂后缀
**示例**: `Amazon Elastic Compute Cloud (EC2) P3 instances`
```python
def generate_full_complex_suffix_pattern(service_data):
    """生成全称复合后缀模式"""
    full_name = service_data['full_name_en']
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    
    return {
        'pattern_name': f"{get_acronym(full_name)}_FULL_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"{re.escape(full_name)}\\s+({suffix_patterns})",
        'priority': 120,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }
```

#### 2. 全称标准模式 (优先级: 115)
**用途**: 精确匹配完整服务名称
**示例**: `Amazon Elastic Compute Cloud (EC2)`
```python
def generate_full_standard_pattern(service_data):
    """生成全称标准模式"""
    full_name = service_data['full_name_en']
    
    return {
        'pattern_name': f"{get_acronym(full_name)}_FULL_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(full_name),
        'priority': 115,
        'notes': 'Standard full name pattern with boundary protection'
    }
```

#### 3. 简称复合后缀模式 (优先级: 110)
**用途**: 匹配简称 + 复杂后缀
**示例**: `Amazon EC2 P3 instances`
```python
def generate_short_complex_suffix_pattern(service_data):
    """生成简称复合后缀模式"""
    short_name = service_data['short_name_en']
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    
    return {
        'pattern_name': f"{get_acronym(short_name)}_SHORT_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"{re.escape(short_name)}\\s+({suffix_patterns})",
        'priority': 110,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }
```

#### 4. 简称标准模式 (优先级: 105)
**用途**: 精确匹配服务简称
**示例**: `Amazon EC2`
```python
def generate_short_standard_pattern(service_data):
    """生成简称标准模式"""
    short_name = service_data['short_name_en']
    
    return {
        'pattern_name': f"{get_acronym(short_name)}_SHORT_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(short_name),
        'priority': 105,
        'notes': 'Standard short name pattern with boundary protection'
    }
```

#### 5. 缩写复合后缀模式 (优先级: 100)
**用途**: 匹配纯缩写 + 后缀
**示例**: `EC2 P3 instances`
```python
def generate_acronym_complex_suffix_pattern(service_data):
    """生成缩写复合后缀模式"""
    acronym = get_acronym(service_data['full_name_en'])
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    
    return {
        'pattern_name': f"{acronym}_ACRONYM_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<![:_-])\\b{acronym}\\s+({suffix_patterns})(?![:_-])",
        'priority': 100,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }
```

#### 6. 缩写标准模式 (优先级: 95)
**用途**: 精确匹配纯缩写
**示例**: `EC2`
```python
def generate_acronym_standard_pattern(service_data):
    """生成缩写标准模式"""
    acronym = get_acronym(service_data['full_name_en'])
    
    return {
        'pattern_name': f"{acronym}_ACRONYM_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<![:_-])\\b{acronym}\\b(?![:_-])",
        'priority': 95,
        'notes': 'Pure acronym pattern with boundary protection'
    }
```

#### 7. 特殊变体模式 (优先级: 125)
**用途**: 处理特殊服务变体
**示例**: `Aurora PostgreSQL`, `RDS for MySQL`
```python
def generate_special_variant_patterns(service_data):
    """生成特殊变体模式"""
    patterns = []
    
    # 数据库引擎变体
    if service_data['service_code'] in ['aurora', 'rds']:
        for engine in ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']:
            patterns.append({
                'pattern_name': f"{service_data['service_code'].upper()}_{engine.upper()}_VARIANT",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"{re.escape(service_data['short_name_en'])}\\s+{engine}\\b",
                'priority': 125,
                'notes': f'Special variant for {engine} engine'
            })
    
    return patterns
```

#### 8. 上下文保护模式 (优先级: 90)
**用途**: 在特定上下文中避免误匹配
**示例**: 避免在ARN、URL中匹配服务名
```python
def generate_context_protected_pattern(service_data):
    """生成上下文保护模式"""
    service_names = [
        service_data['full_name_en'],
        service_data['short_name_en'],
        get_acronym(service_data['full_name_en'])
    ]
    
    patterns = []
    for name in service_names:
        patterns.append({
            'pattern_name': f"{get_acronym(name)}_CONTEXT_PROTECTED",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)(?<!https?://[^\\s]*){re.escape(name)}(?![^\\s]*\\.[a-z]{{2,4}})",
            'priority': 90,
            'notes': 'Context-aware pattern avoiding ARN and URL matches'
        })
    
    return patterns
```

## 后缀模式定义

### 通用后缀模式
```python
COMMON_SUFFIX_PATTERNS = {
    'instance': r'((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))',
    'volume': r'(volume\(s\)?|volumes|volume)',
    'database': r'(for\s+(?:PostgreSQL|MySQL|MariaDB|Oracle|SQL\s+Server))',
    'cluster': r'(cluster|clusters)',
    'service': r'(service|services)',
    'resource': r'(resource|resources)'
}

def get_service_suffix_patterns(service_code):
    """根据服务代码获取适用的后缀模式"""
    suffix_mapping = {
        'ec2': ['instance', 'volume'],
        'rds': ['database', 'cluster'],
        'ecs': ['cluster', 'service'],
        'eks': ['cluster'],
        's3': ['resource'],
        'lambda': ['resource']
    }
    
    applicable_suffixes = suffix_mapping.get(service_code, ['resource'])
    return '|'.join([COMMON_SUFFIX_PATTERNS[suffix] for suffix in applicable_suffixes])
```

## 边界保护机制

### 核心保护函数
```python
def generate_boundary_protected_pattern(service_name):
    """生成带完整边界保护的模式"""
    # ARN保护：避免在ARN中匹配
    arn_protection = r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)'
    
    # URL保护：避免在URL中匹配
    url_protection = r'(?<!https?://[^\s]*)'
    
    # 代码保护：避免在代码标识符中匹配
    code_protection = r'(?<![:_-])'
    
    # 后续保护：避免作为更大标识符的一部分
    trailing_protection = r'(?![:_-])'
    url_trailing_protection = r'(?![^\s]*\.[a-z]{2,4})'
    
    return f"{arn_protection}{url_protection}{code_protection}\\b{re.escape(service_name)}\\b{trailing_protection}{url_trailing_protection}"
```

### 特殊上下文处理
```python
def generate_context_aware_patterns(service_data):
    """生成上下文感知的模式"""
    patterns = []
    
    # CLI命令上下文
    cli_pattern = {
        'pattern_name': f"{get_acronym(service_data['full_name_en'])}_CLI_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=aws\\s){service_data['service_code']}(?=\\s)",
        'priority': 130,
        'notes': 'CLI command context pattern'
    }
    patterns.append(cli_pattern)
    
    # 配置文件上下文
    config_pattern = {
        'pattern_name': f"{get_acronym(service_data['full_name_en'])}_CONFIG_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=\"){service_data['service_code']}(?=\")",
        'priority': 128,
        'notes': 'Configuration file context pattern'
    }
    patterns.append(config_pattern)
    
    return patterns
```

## 特殊服务处理

### Aurora服务族
```python
def generate_aurora_patterns():
    """生成Aurora服务的特殊模式"""
    base_patterns = [
        {
            'service_name': 'Amazon Aurora',
            'variants': ['PostgreSQL', 'MySQL'],
            'priority_base': 125
        }
    ]
    
    patterns = []
    for base in base_patterns:
        # 通用Aurora模式
        patterns.append({
            'pattern_name': 'AURORA_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
            'priority': base['priority_base'],
            'notes': 'General Aurora pattern excluding specific variants'
        })
        
        # 特定引擎变体
        for variant in base['variants']:
            patterns.append({
                'pattern_name': f'AURORA_{variant.upper()}_VARIANT',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{variant}\\b",
                'priority': base['priority_base'] + 5,
                'notes': f'Aurora {variant} specific variant'
            })
    
    return patterns
```

### Health服务族
```python
def generate_health_patterns():
    """生成Health服务的特殊模式"""
    return [
        {
            'pattern_name': 'HEALTH_DASHBOARD_FULL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\s+Dashboard',
            'priority': 125,
            'notes': 'Full Health Dashboard pattern'
        },
        {
            'pattern_name': 'HEALTH_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\b(?!Lake|Imaging|\s+Dashboard)',
            'priority': 120,
            'notes': 'General Health pattern excluding other Health services'
        }
    ]
```

### RDS服务族
```python
def generate_rds_patterns():
    """生成RDS服务的特殊模式"""
    engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
    patterns = []
    
    for engine in engines:
        # RDS for Engine模式
        patterns.append({
            'pattern_name': f'RDS_FOR_{engine.replace(" ", "_").upper()}',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
            'priority': 125,
            'notes': f'RDS for {engine} specific pattern'
        })
    
    # 通用RDS模式（排除特定引擎）
    engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
    patterns.append({
        'pattern_name': 'RDS_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
        'priority': 115,
        'notes': 'General RDS pattern excluding specific engine variants'
    })
    
    return patterns
```

## 模式生成主函数

### 完整模式生成器
```python
def generate_comprehensive_service_patterns(service_data):
    """为单个服务生成完整的模式集合"""
    patterns = []
    
    # 1. 基础模式（8种核心类型）
    if has_complex_suffix_support(service_data):
        patterns.append(generate_full_complex_suffix_pattern(service_data))
        patterns.append(generate_short_complex_suffix_pattern(service_data))
        patterns.append(generate_acronym_complex_suffix_pattern(service_data))
    
    patterns.append(generate_full_standard_pattern(service_data))
    patterns.append(generate_short_standard_pattern(service_data))
    patterns.append(generate_acronym_standard_pattern(service_data))
    
    # 2. 特殊变体模式
    if has_special_variants(service_data):
        patterns.extend(generate_special_variant_patterns(service_data))
    
    # 3. 上下文保护模式
    patterns.extend(generate_context_aware_patterns(service_data))
    
    # 4. 服务特定模式
    service_code = service_data['service_code']
    if service_code == 'aurora':
        patterns.extend(generate_aurora_patterns())
    elif service_code == 'health':
        patterns.extend(generate_health_patterns())
    elif service_code == 'rds':
        patterns.extend(generate_rds_patterns())
    
    return patterns

def batch_generate_all_patterns(services_list):
    """批量生成所有服务的模式"""
    all_patterns = []
    
    for service_data in services_list:
        service_patterns = generate_comprehensive_service_patterns(service_data)
        
        # 添加服务关联信息
        for pattern in service_patterns:
            pattern['related_service_id'] = service_data['id']
            pattern['service_code'] = service_data['service_code']
            pattern['created_at'] = datetime.now()
            pattern['is_active'] = True
        
        all_patterns.extend(service_patterns)
    
    # 按优先级排序
    all_patterns.sort(key=lambda x: x['priority'], reverse=True)
    
    return all_patterns
```

## 工具函数

### 辅助函数集合
```python
def get_acronym(service_name):
    """从服务名称中提取缩写"""
    match = re.search(r'\(([^)]+)\)', service_name)
    if match:
        return match.group(1)
    
    # 如果没有括号，尝试从首字母生成
    words = service_name.replace('Amazon ', '').replace('AWS ', '').split()
    return ''.join([word[0].upper() for word in words if word[0].isupper()])

def has_complex_suffix_support(service_data):
    """判断服务是否支持复杂后缀"""
    suffix_supported_services = ['ec2', 'rds', 'ecs', 'eks', 'ebs']
    return service_data['service_code'] in suffix_supported_services

def has_special_variants(service_data):
    """判断服务是否有特殊变体"""
    variant_services = ['aurora', 'rds', 'health', 'iam']
    return service_data['service_code'] in variant_services

def validate_pattern_syntax(pattern):
    """验证正则表达式模式的语法正确性"""
    try:
        re.compile(pattern['regex_string'])
        return True, None
    except re.error as e:
        return False, str(e)

def detect_pattern_conflicts(patterns):
    """检测模式之间的潜在冲突"""
    conflicts = []
    
    for i, pattern1 in enumerate(patterns):
        for j, pattern2 in enumerate(patterns[i+1:], i+1):
            if pattern1['priority'] == pattern2['priority']:
                conflicts.append({
                    'type': 'priority_conflict',
                    'patterns': [pattern1['pattern_name'], pattern2['pattern_name']],
                    'priority': pattern1['priority']
                })
    
    return conflicts
```

## 数据库集成

### 模式存储结构
```sql
-- 增强的regex_patterns表结构
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL UNIQUE,
    pattern_type VARCHAR(50) NOT NULL,
    regex_string TEXT NOT NULL,
    related_service_id BIGINT REFERENCES service_names(id),
    service_code VARCHAR(50),
    priority INTEGER NOT NULL DEFAULT 100,
    notes TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    validation_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- 索引优化
    INDEX idx_regex_patterns_priority (priority DESC, id ASC),
    INDEX idx_regex_patterns_service (related_service_id),
    INDEX idx_regex_patterns_type_active (pattern_type, is_active),
    INDEX idx_regex_patterns_service_code (service_code)
);
```

### 批量插入优化
```python
def batch_insert_patterns_optimized(patterns):
    """优化的批量模式插入"""
    # 验证所有模式
    validated_patterns = []
    for pattern in patterns:
        is_valid, error = validate_pattern_syntax(pattern)
        if is_valid:
            pattern['validation_status'] = 'valid'
            validated_patterns.append(pattern)
        else:
            logger.warning(f"Invalid pattern {pattern['pattern_name']}: {error}")
            pattern['validation_status'] = 'invalid'
    
    # 检测冲突
    conflicts = detect_pattern_conflicts(validated_patterns)
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")
    
    # 批量插入
    insert_sql = """
    INSERT INTO regex_patterns 
        (pattern_name, pattern_type, regex_string, related_service_id, 
         service_code, priority, notes, validation_status, is_active)
    VALUES %s
    ON CONFLICT (pattern_name) 
    DO UPDATE SET
        regex_string = EXCLUDED.regex_string,
        priority = EXCLUDED.priority,
        notes = EXCLUDED.notes,
        validation_status = EXCLUDED.validation_status,
        updated_at = NOW()
    """
    
    pattern_data = [
        (p['pattern_name'], p['pattern_type'], p['regex_string'], 
         p['related_service_id'], p['service_code'], p['priority'], 
         p['notes'], p['validation_status'], p['is_active'])
        for p in validated_patterns
    ]
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            psycopg2.extras.execute_values(
                cur, insert_sql, pattern_data, template=None, page_size=100
            )
    
    return len(validated_patterns), len(conflicts)
```



## 使用示例

### 完整使用流程
```python
# 1. 从数据库获取服务数据
services = get_all_active_services()

# 2. 生成所有模式
all_patterns = batch_generate_all_patterns(services)

# 3. 验证和优化
validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)

print(f"Generated {len(all_patterns)} patterns")
print(f"Validated {validated_count} patterns")
print(f"Detected {len(conflicts)} conflicts")
```

## 总结

本重构的模式生成策略具有以下特点：

### 全面性
- **8种核心模式类型**：覆盖所有常见使用场景
- **特殊服务支持**：针对Aurora、Health、RDS等服务的专门处理
- **上下文感知**：避免ARN、URL等上下文中的误匹配

### 精确性
- **分层优先级**：90-130的精确优先级控制
- **边界保护**：多重保护机制避免误匹配
- **语法验证**：自动验证正则表达式语法正确性

### 精简性
- **模块化设计**：每种模式类型独立生成
- **批量优化**：高效的批量处理和数据库操作
- **冲突检测**：自动检测和报告模式冲突

该策略能够满足AWS服务同步系统的所有复杂需求，同时保持代码的可维护性和扩展性。