"""
AWS服务同步系统 - 优先级自动分配辅助模块

该模块提供了与数据库函数 assign_pattern_priority 的集成接口，
用于在Python代码中自动分配正则表达式模式的优先级。
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime

from utils.structured_logger import StructuredLogger
from storage.rds_client import RDSClient

logger = logging.getLogger(__name__)


class PriorityAssignmentHelper:
    """
    优先级自动分配辅助类
    
    提供与数据库函数 assign_pattern_priority 的集成接口，
    支持单个模式和批量模式的优先级自动分配。
    """
    
    def __init__(self, db_client: RDSClient):
        """
        初始化优先级分配辅助器
        
        Args:
            db_client: 数据库客户端实例
        """
        self.db_client = db_client
        self.logger = StructuredLogger("priority_assignment_helper")
    
    def assign_single_pattern_priority(
        self, 
        pattern_name: str,
        pattern_type: str,
        regex_string: str,
        related_service_id: int,
        is_compound: bool = False,
        suffix_group: Optional[str] = None
    ) -> Dict[str, int]:
        """
        为单个正则模式分配优先级
        
        Args:
            pattern_name: 模式名称
            pattern_type: 模式类型 (SERVICE_NAME, TIMEZONE, etc.)
            regex_string: 正则表达式字符串
            related_service_id: 关联的服务ID
            is_compound: 是否为复合模式
            suffix_group: 复合后缀组标识
            
        Returns:
            包含 priority_tier 和 priority_value 的字典
            
        Example:
            >>> helper = PriorityAssignmentHelper(db_client)
            >>> priority = helper.assign_single_pattern_priority(
            ...     pattern_name="EC2_FULL_STANDARD",
            ...     pattern_type="SERVICE_NAME",
            ...     regex_string="Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)",
            ...     related_service_id=123,
            ...     is_compound=False
            ... )
            >>> print(priority)
            {'priority_tier': 3, 'priority_value': 7}
        """
        try:
            query = """
            SELECT tier, value 
            FROM assign_pattern_priority(%s, %s, %s, %s, %s, %s)
            """
            
            params = (
                pattern_name,
                pattern_type,
                regex_string,
                related_service_id,
                is_compound,
                suffix_group
            )
            
            self.logger.debug("Assigning priority for pattern", {
                "pattern_name": pattern_name,
                "pattern_type": pattern_type,
                "related_service_id": related_service_id,
                "is_compound": is_compound
            })
            
            result = self.db_client.execute_query(query, params)
            
            if result and len(result) > 0:
                tier, value = result[0]
                priority_info = {
                    'priority_tier': tier,
                    'priority_value': value
                }
                
                self.logger.info("Successfully assigned priority", {
                    "pattern_name": pattern_name,
                    "priority_tier": tier,
                    "priority_value": value,
                    "computed_priority": (11 - tier) * 10 + value
                })
                
                return priority_info
            else:
                # 返回默认优先级
                default_priority = {
                    'priority_tier': 8,
                    'priority_value': 5
                }
                
                self.logger.warning("No priority returned from function, using default", {
                    "pattern_name": pattern_name,
                    "default_priority": default_priority
                })
                
                return default_priority
                
        except Exception as e:
            self.logger.error("Failed to assign priority via database function", {
                "pattern_name": pattern_name,
                "error": str(e)
            })
            
            # 返回默认优先级
            return {
                'priority_tier': 8,
                'priority_value': 5
            }
    
    def assign_batch_pattern_priorities(
        self, 
        patterns: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        批量为多个正则模式分配优先级
        
        Args:
            patterns: 包含模式信息的字典列表，每个字典应包含：
                - pattern_name: str
                - pattern_type: str  
                - regex_string: str
                - related_service_id: int
                - is_compound: bool (可选，默认False)
                - suffix_group: str (可选)
                
        Returns:
            更新了优先级信息的模式列表
            
        Example:
            >>> patterns = [
            ...     {
            ...         'pattern_name': 'EC2_FULL_STANDARD',
            ...         'pattern_type': 'SERVICE_NAME',
            ...         'regex_string': 'Amazon\\s+EC2\\b',
            ...         'related_service_id': 123,
            ...         'is_compound': False
            ...     },
            ...     {
            ...         'pattern_name': 'EC2_FULL_COMPOUND',
            ...         'pattern_type': 'SERVICE_NAME', 
            ...         'regex_string': 'Amazon\\s+EC2\\s+(instance|instances)',
            ...         'related_service_id': 123,
            ...         'is_compound': True,
            ...         'suffix_group': 'instance'
            ...     }
            ... ]
            >>> updated_patterns = helper.assign_batch_pattern_priorities(patterns)
        """
        updated_patterns = []
        successful_count = 0
        failed_count = 0
        
        self.logger.info("Starting batch priority assignment", {
            "total_patterns": len(patterns)
        })
        
        for i, pattern in enumerate(patterns):
            try:
                # 提取必需的参数
                pattern_name = pattern['pattern_name']
                pattern_type = pattern['pattern_type']
                regex_string = pattern['regex_string']
                related_service_id = pattern['related_service_id']
                is_compound = pattern.get('is_compound', False)
                suffix_group = pattern.get('suffix_group', None)
                
                # 分配优先级
                priority_info = self.assign_single_pattern_priority(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    regex_string=regex_string,
                    related_service_id=related_service_id,
                    is_compound=is_compound,
                    suffix_group=suffix_group
                )
                
                # 更新模式信息
                updated_pattern = pattern.copy()
                updated_pattern.update(priority_info)
                updated_patterns.append(updated_pattern)
                successful_count += 1
                
            except Exception as e:
                self.logger.error("Failed to assign priority for pattern in batch", {
                    "pattern_index": i,
                    "pattern_name": pattern.get('pattern_name', 'unknown'),
                    "error": str(e)
                })
                
                # 添加默认优先级
                updated_pattern = pattern.copy()
                updated_pattern.update({
                    'priority_tier': 8,
                    'priority_value': 5
                })
                updated_patterns.append(updated_pattern)
                failed_count += 1
        
        self.logger.info("Completed batch priority assignment", {
            "total_patterns": len(patterns),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "success_rate": successful_count / len(patterns) if patterns else 0
        })
        
        return updated_patterns
    
    def validate_priority_assignment(
        self, 
        pattern_name: str, 
        expected_tier: int, 
        expected_value: int
    ) -> bool:
        """
        验证优先级分配是否符合预期
        
        Args:
            pattern_name: 模式名称
            expected_tier: 期望的优先级层级
            expected_value: 期望的优先级值
            
        Returns:
            验证是否通过
        """
        try:
            query = """
            SELECT priority_tier, priority_value 
            FROM regex_patterns 
            WHERE pattern_name = %s AND is_active = TRUE
            """
            
            result = self.db_client.execute_query(query, (pattern_name,))
            
            if result and len(result) > 0:
                actual_tier, actual_value = result[0]
                is_valid = (actual_tier == expected_tier and actual_value == expected_value)
                
                self.logger.info("Priority validation result", {
                    "pattern_name": pattern_name,
                    "expected_tier": expected_tier,
                    "expected_value": expected_value,
                    "actual_tier": actual_tier,
                    "actual_value": actual_value,
                    "is_valid": is_valid
                })
                
                return is_valid
            else:
                self.logger.warning("Pattern not found for validation", {
                    "pattern_name": pattern_name
                })
                return False
                
        except Exception as e:
            self.logger.error("Failed to validate priority assignment", {
                "pattern_name": pattern_name,
                "error": str(e)
            })
            return False
