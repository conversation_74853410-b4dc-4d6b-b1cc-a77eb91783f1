"""
AWS服务正则表达式模式生成器

基于AWS服务模式生成系统设计规范实现的完整模式生成器。
支持8种核心模式类型、边界保护、复合词处理和特殊服务变体。
"""

import re
import logging
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)

# 通用后缀模式定义
COMMON_SUFFIX_PATTERNS = {
    'instance': r'((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))',
    'volume': r'(volume\(s\)?|volumes|volume)',
    'database': r'(for\s+(?:PostgreSQL|MySQL|MariaDB|Oracle|SQL\s+Server))',
    'cluster': r'(cluster|clusters)',
    'service': r'(service|services)',
    'resource': r'(resource|resources)'
}

# 服务后缀映射
SERVICE_SUFFIX_MAPPING = {
    'ec2': ['instance', 'volume'],
    'rds': ['database', 'cluster'],
    'ecs': ['cluster', 'service'],
    'eks': ['cluster'],
    's3': ['resource'],
    'lambda': ['resource'],
    'ebs': ['volume'],
    'aurora': ['database', 'cluster']
}

# 特殊服务变体配置
SPECIAL_SERVICE_VARIANTS = {
    'aurora': ['PostgreSQL', 'MySQL'],
    'rds': ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server'],
    'health': ['Dashboard'],
    'iam': ['Identity Center', 'Access Analyzer']
}


def get_acronym(service_name: str) -> str:
    """从服务名称中提取缩写"""
    # 首先尝试从括号中提取
    match = re.search(r'\(([^)]+)\)', service_name)
    if match:
        return match.group(1)
    
    # 如果没有括号，尝试从首字母生成
    words = service_name.replace('Amazon ', '').replace('AWS ', '').split()
    return ''.join([word[0].upper() for word in words if word and word[0].isupper()])


def get_service_suffix_patterns(service_code: str) -> str:
    """根据服务代码获取适用的后缀模式"""
    applicable_suffixes = SERVICE_SUFFIX_MAPPING.get(service_code, ['resource'])
    return '|'.join([COMMON_SUFFIX_PATTERNS[suffix] for suffix in applicable_suffixes])


def has_complex_suffix_support(service_data: Dict[str, Any]) -> bool:
    """判断服务是否支持复杂后缀"""
    suffix_supported_services = ['ec2', 'rds', 'ecs', 'eks', 'ebs', 'aurora']
    return service_data.get('service_code', '').lower() in suffix_supported_services


def has_special_variants(service_data: Dict[str, Any]) -> bool:
    """判断服务是否有特殊变体"""
    variant_services = ['aurora', 'rds', 'health', 'iam']
    return service_data.get('service_code', '').lower() in variant_services


def generate_boundary_protected_pattern(service_name: str) -> str:
    """生成带完整边界保护的模式"""
    # ARN保护：避免在ARN中匹配
    arn_protection = r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)'
    
    # URL保护：避免在URL中匹配
    url_protection = r'(?<!https?://[^\s]*)'
    
    # 代码保护：避免在代码标识符中匹配
    code_protection = r'(?<![:_-])'
    
    # 后续保护：避免作为更大标识符的一部分
    trailing_protection = r'(?![:_-])'
    url_trailing_protection = r'(?![^\s]*\.[a-z]{2,4})'
    
    escaped_name = re.escape(service_name)
    return f"{arn_protection}{url_protection}{code_protection}\\b{escaped_name}\\b{trailing_protection}{url_trailing_protection}"


def generate_full_complex_suffix_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成全称复合后缀模式 (优先级: 120)"""
    full_name = service_data['full_name_en']
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    acronym = get_acronym(full_name)
    
    return {
        'pattern_name': f"{acronym}_FULL_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"{re.escape(full_name)}\\s+({suffix_patterns})",
        'priority': 120,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }


def generate_full_standard_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成全称标准模式 (优先级: 115)"""
    full_name = service_data['full_name_en']
    acronym = get_acronym(full_name)
    
    return {
        'pattern_name': f"{acronym}_FULL_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(full_name),
        'priority': 115,
        'notes': 'Standard full name pattern with boundary protection'
    }


def generate_short_complex_suffix_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成简称复合后缀模式 (优先级: 110)"""
    short_name = service_data['short_name_en']
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    acronym = get_acronym(service_data['full_name_en'])
    
    return {
        'pattern_name': f"{acronym}_SHORT_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"{re.escape(short_name)}\\s+({suffix_patterns})",
        'priority': 110,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }


def generate_short_standard_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成简称标准模式 (优先级: 105)"""
    short_name = service_data['short_name_en']
    acronym = get_acronym(service_data['full_name_en'])
    
    return {
        'pattern_name': f"{acronym}_SHORT_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(short_name),
        'priority': 105,
        'notes': 'Standard short name pattern with boundary protection'
    }


def generate_acronym_complex_suffix_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成缩写复合后缀模式 (优先级: 100)"""
    acronym = get_acronym(service_data['full_name_en'])
    suffix_patterns = get_service_suffix_patterns(service_data['service_code'])
    
    return {
        'pattern_name': f"{acronym}_ACRONYM_COMPLEX_SUFFIX",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<![:_-])\\b{acronym}\\s+({suffix_patterns})(?![:_-])",
        'priority': 100,
        'notes': 'isCompoundWithSuffix: true, suffixGroup: 1'
    }


def generate_acronym_standard_pattern(service_data: Dict[str, Any]) -> Dict[str, Any]:
    """生成缩写标准模式 (优先级: 95)"""
    acronym = get_acronym(service_data['full_name_en'])
    
    return {
        'pattern_name': f"{acronym}_ACRONYM_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<![:_-])\\b{acronym}\\b(?![:_-])",
        'priority': 95,
        'notes': 'Pure acronym pattern with boundary protection'
    }


def generate_special_variant_patterns(service_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成特殊变体模式 (优先级: 125)"""
    patterns = []
    service_code = service_data['service_code'].lower()
    
    if service_code not in SPECIAL_SERVICE_VARIANTS:
        return patterns
    
    variants = SPECIAL_SERVICE_VARIANTS[service_code]
    short_name = service_data['short_name_en']
    
    for variant in variants:
        patterns.append({
            'pattern_name': f"{service_code.upper()}_{variant.replace(' ', '_').upper()}_VARIANT",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"{re.escape(short_name)}\\s+{re.escape(variant)}\\b",
            'priority': 125,
            'notes': f'Special variant for {variant}'
        })
    
    return patterns


def generate_context_aware_patterns(service_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """生成上下文感知的模式"""
    patterns = []
    service_code = service_data['service_code']
    acronym = get_acronym(service_data['full_name_en'])
    
    # CLI命令上下文模式
    cli_pattern = {
        'pattern_name': f"{acronym}_CLI_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=aws\\s){service_code}(?=\\s)",
        'priority': 130,
        'notes': 'CLI command context pattern'
    }
    patterns.append(cli_pattern)
    
    # 配置文件上下文模式
    config_pattern = {
        'pattern_name': f"{acronym}_CONFIG_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f'(?<="){service_code}(?=")',
        'priority': 128,
        'notes': 'Configuration file context pattern'
    }
    patterns.append(config_pattern)
    
    return patterns


def generate_aurora_patterns() -> List[Dict[str, Any]]:
    """生成Aurora服务的特殊模式"""
    patterns = []
    
    # 通用Aurora模式（排除特定变体）
    patterns.append({
        'pattern_name': 'AURORA_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
        'priority': 125,
        'notes': 'General Aurora pattern excluding specific variants'
    })
    
    # 特定引擎变体
    for variant in ['PostgreSQL', 'MySQL']:
        patterns.append({
            'pattern_name': f'AURORA_{variant.upper()}_VARIANT',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{variant}\\b",
            'priority': 130,
            'notes': f'Aurora {variant} specific variant'
        })
    
    return patterns


def generate_health_patterns() -> List[Dict[str, Any]]:
    """生成Health服务的特殊模式"""
    return [
        {
            'pattern_name': 'HEALTH_DASHBOARD_FULL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\s+Dashboard',
            'priority': 125,
            'notes': 'Full Health Dashboard pattern'
        },
        {
            'pattern_name': 'HEALTH_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\b(?!Lake|Imaging|\s+Dashboard)',
            'priority': 120,
            'notes': 'General Health pattern excluding other Health services'
        }
    ]


def generate_rds_patterns() -> List[Dict[str, Any]]:
    """生成RDS服务的特殊模式"""
    engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
    patterns = []
    
    for engine in engines:
        # RDS for Engine模式
        patterns.append({
            'pattern_name': f'RDS_FOR_{engine.replace(" ", "_").upper()}',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
            'priority': 125,
            'notes': f'RDS for {engine} specific pattern'
        })
    
    # 通用RDS模式（排除特定引擎）
    engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
    patterns.append({
        'pattern_name': 'RDS_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
        'priority': 115,
        'notes': 'General RDS pattern excluding specific engine variants'
    })
    
    return patterns


def generate_comprehensive_service_patterns(service_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """为单个服务生成完整的模式集合"""
    patterns = []
    
    # 1. 基础模式（8种核心类型）
    if has_complex_suffix_support(service_data):
        patterns.append(generate_full_complex_suffix_pattern(service_data))
        patterns.append(generate_short_complex_suffix_pattern(service_data))
        patterns.append(generate_acronym_complex_suffix_pattern(service_data))
    
    patterns.append(generate_full_standard_pattern(service_data))
    patterns.append(generate_short_standard_pattern(service_data))
    patterns.append(generate_acronym_standard_pattern(service_data))
    
    # 2. 特殊变体模式
    if has_special_variants(service_data):
        patterns.extend(generate_special_variant_patterns(service_data))
    
    # 3. 上下文保护模式
    patterns.extend(generate_context_aware_patterns(service_data))
    
    # 4. 服务特定模式
    service_code = service_data['service_code'].lower()
    if service_code == 'aurora':
        patterns.extend(generate_aurora_patterns())
    elif service_code == 'health':
        patterns.extend(generate_health_patterns())
    elif service_code == 'rds':
        patterns.extend(generate_rds_patterns())
    
    return patterns


def batch_generate_all_patterns(services_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """批量生成所有服务的模式"""
    all_patterns = []
    
    for service_data in services_list:
        try:
            service_patterns = generate_comprehensive_service_patterns(service_data)
            
            # 添加服务关联信息
            for pattern in service_patterns:
                pattern['related_service_id'] = service_data.get('id')
                pattern['service_code'] = service_data.get('service_code')
                pattern['created_at'] = datetime.now()
                pattern['is_active'] = True
            
            all_patterns.extend(service_patterns)
            logger.debug(f"Generated {len(service_patterns)} patterns for service {service_data.get('service_code')}")
            
        except Exception as e:
            logger.error(f"Failed to generate patterns for service {service_data.get('service_code')}: {e}")
            continue
    
    # 按优先级排序
    all_patterns.sort(key=lambda x: x['priority'], reverse=True)
    
    logger.info(f"Generated total {len(all_patterns)} patterns for {len(services_list)} services")
    return all_patterns


def validate_pattern_syntax(pattern: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
    """验证正则表达式模式的语法正确性"""
    try:
        re.compile(pattern['regex_string'], re.IGNORECASE)
        return True, None
    except re.error as e:
        return False, str(e)


def detect_pattern_conflicts(patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """检测模式之间的潜在冲突"""
    conflicts = []
    
    # 检查优先级冲突
    priority_groups = {}
    for pattern in patterns:
        priority = pattern['priority']
        if priority not in priority_groups:
            priority_groups[priority] = []
        priority_groups[priority].append(pattern)
    
    for priority, group in priority_groups.items():
        if len(group) > 1:
            conflicts.append({
                'type': 'priority_conflict',
                'patterns': [p['pattern_name'] for p in group],
                'priority': priority,
                'count': len(group)
            })
    
    return conflicts


def batch_insert_patterns_optimized(patterns: List[Dict[str, Any]]) -> Tuple[int, List[Dict[str, Any]]]:
    """优化的批量模式插入（模拟实现）"""
    # 验证所有模式
    validated_patterns = []
    validation_errors = []
    
    for pattern in patterns:
        is_valid, error = validate_pattern_syntax(pattern)
        if is_valid:
            pattern['validation_status'] = 'valid'
            validated_patterns.append(pattern)
        else:
            logger.warning(f"Invalid pattern {pattern['pattern_name']}: {error}")
            pattern['validation_status'] = 'invalid'
            validation_errors.append({
                'pattern_name': pattern['pattern_name'],
                'error': error
            })
    
    # 检测冲突
    conflicts = detect_pattern_conflicts(validated_patterns)
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")
        for conflict in conflicts:
            logger.warning(f"Priority {conflict['priority']} conflict: {conflict['patterns']}")
    
    # 在实际实现中，这里会执行数据库插入操作
    logger.info(f"Would insert {len(validated_patterns)} validated patterns to database")
    
    return len(validated_patterns), conflicts


# 使用示例
if __name__ == "__main__":
    # 示例服务数据
    sample_service = {
        'id': 1,
        'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
        'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
        'short_name_en': 'Amazon EC2',
        'service_code': 'ec2'
    }
    
    # 生成模式
    patterns = generate_comprehensive_service_patterns(sample_service)
    
    print(f"Generated {len(patterns)} patterns for EC2:")
    for pattern in sorted(patterns, key=lambda x: x['priority'], reverse=True):
        print(f"  {pattern['pattern_name']} (Priority: {pattern['priority']})")
        print(f"    Regex: {pattern['regex_string']}")
        print(f"    Notes: {pattern.get('notes', 'N/A')}")
        print()