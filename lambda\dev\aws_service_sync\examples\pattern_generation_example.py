"""
AWS服务模式生成系统使用示例

展示如何在实际项目中使用新的模式生成策略，
包括完整的集成流程、性能优化和错误处理。
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Any

# 导入模式生成器
from processors.regex_pattern_generator import (
    generate_comprehensive_service_patterns,
    batch_generate_all_patterns,
    validate_pattern_syntax,
    detect_pattern_conflicts
)

# 导入存储管理器
from storage.pattern_storage import (
    create_pattern_storage_manager,
    batch_insert_patterns_optimized
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_basic_pattern_generation():
    """示例1: 基本模式生成"""
    print("=" * 60)
    print("示例1: 基本模式生成")
    print("=" * 60)
    
    # 定义服务数据
    ec2_service = {
        'id': 1,
        'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
        'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
        'short_name_en': 'Amazon EC2',
        'service_code': 'ec2'
    }
    
    # 生成模式
    patterns = generate_comprehensive_service_patterns(ec2_service)
    
    print(f"为EC2服务生成了 {len(patterns)} 个模式:")
    for pattern in sorted(patterns, key=lambda x: x['priority'], reverse=True):
        print(f"  {pattern['pattern_name']} (优先级: {pattern['priority']})")
        print(f"    正则表达式: {pattern['regex_string']}")
        print(f"    说明: {pattern.get('notes', 'N/A')}")
        print()


def example_batch_generation():
    """示例2: 批量模式生成"""
    print("=" * 60)
    print("示例2: 批量模式生成")
    print("=" * 60)
    
    # 定义多个服务
    services = [
        {
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        },
        {
            'id': 2,
            'authoritative_full_name': 'Amazon Relational Database Service (RDS)',
            'full_name_en': 'Amazon Relational Database Service (RDS)',
            'short_name_en': 'Amazon RDS',
            'service_code': 'rds'
        },
        {
            'id': 3,
            'authoritative_full_name': 'Amazon Aurora',
            'full_name_en': 'Amazon Aurora',
            'short_name_en': 'Aurora',
            'service_code': 'aurora'
        },
        {
            'id': 4,
            'authoritative_full_name': 'Amazon Simple Storage Service (S3)',
            'full_name_en': 'Amazon Simple Storage Service (S3)',
            'short_name_en': 'Amazon S3',
            'service_code': 's3'
        }
    ]
    
    # 批量生成模式
    all_patterns = batch_generate_all_patterns(services)
    
    print(f"为 {len(services)} 个服务生成了 {len(all_patterns)} 个模式")
    
    # 按服务分组统计
    service_stats = {}
    for pattern in all_patterns:
        service_code = pattern['service_code']
        if service_code not in service_stats:
            service_stats[service_code] = 0
        service_stats[service_code] += 1
    
    print("\n各服务模式数量:")
    for service_code, count in service_stats.items():
        print(f"  {service_code}: {count} 个模式")
    
    # 优先级分布
    priority_stats = {}
    for pattern in all_patterns:
        priority = pattern['priority']
        if priority not in priority_stats:
            priority_stats[priority] = 0
        priority_stats[priority] += 1
    
    print("\n优先级分布:")
    for priority in sorted(priority_stats.keys(), reverse=True):
        print(f"  优先级 {priority}: {priority_stats[priority]} 个模式")


def example_pattern_validation():
    """示例3: 模式验证和冲突检测"""
    print("=" * 60)
    print("示例3: 模式验证和冲突检测")
    print("=" * 60)
    
    # 生成一些模式
    services = [
        {
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        },
        {
            'id': 2,
            'authoritative_full_name': 'Amazon Relational Database Service (RDS)',
            'full_name_en': 'Amazon Relational Database Service (RDS)',
            'short_name_en': 'Amazon RDS',
            'service_code': 'rds'
        }
    ]
    
    all_patterns = batch_generate_all_patterns(services)
    
    # 验证模式语法
    valid_count = 0
    invalid_patterns = []
    
    for pattern in all_patterns:
        is_valid, error = validate_pattern_syntax(pattern)
        if is_valid:
            valid_count += 1
        else:
            invalid_patterns.append({
                'name': pattern['pattern_name'],
                'error': error
            })
    
    print(f"模式验证结果:")
    print(f"  有效模式: {valid_count}")
    print(f"  无效模式: {len(invalid_patterns)}")
    
    if invalid_patterns:
        print("\n无效模式详情:")
        for invalid in invalid_patterns:
            print(f"  {invalid['name']}: {invalid['error']}")
    
    # 检测冲突
    conflicts = detect_pattern_conflicts(all_patterns)
    print(f"\n冲突检测结果:")
    print(f"  发现 {len(conflicts)} 个冲突")
    
    for conflict in conflicts:
        print(f"  {conflict['type']}: {conflict}")


def example_pattern_matching():
    """示例4: 模式匹配测试"""
    print("=" * 60)
    print("示例4: 模式匹配测试")
    print("=" * 60)
    
    import re
    
    # 生成EC2模式
    ec2_service = {
        'id': 1,
        'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
        'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
        'short_name_en': 'Amazon EC2',
        'service_code': 'ec2'
    }
    
    patterns = generate_comprehensive_service_patterns(ec2_service)
    
    # 编译正则表达式
    compiled_patterns = []
    for pattern in patterns:
        try:
            compiled = re.compile(pattern['regex_string'], re.IGNORECASE)
            compiled_patterns.append({
                'name': pattern['pattern_name'],
                'regex': compiled,
                'priority': pattern['priority']
            })
        except re.error as e:
            print(f"模式编译失败 {pattern['pattern_name']}: {e}")
    
    # 测试文本
    test_cases = [
        {
            'text': 'Amazon Elastic Compute Cloud (EC2)',
            'should_match': True,
            'description': '完整服务名称'
        },
        {
            'text': 'Amazon EC2',
            'should_match': True,
            'description': '标准简称'
        },
        {
            'text': 'EC2',
            'should_match': True,
            'description': '纯缩写'
        },
        {
            'text': 'Amazon EC2 P3 instances',
            'should_match': True,
            'description': '复合后缀'
        },
        {
            'text': 'EC2 instance family',
            'should_match': True,
            'description': '缩写 + 后缀'
        },
        {
            'text': 'arn:aws:ec2:us-east-1:123456789012:instance/i-1234567890abcdef0',
            'should_match': False,
            'description': 'ARN中的EC2（应被保护）'
        },
        {
            'text': 'https://console.aws.amazon.com/ec2/',
            'should_match': False,
            'description': 'URL中的EC2（应被保护）'
        },
        {
            'text': 'my-ec2-instance',
            'should_match': False,
            'description': '代码标识符中的EC2（应被保护）'
        }
    ]
    
    print("模式匹配测试结果:")
    print("-" * 40)
    
    for test_case in test_cases:
        text = test_case['text']
        should_match = test_case['should_match']
        description = test_case['description']
        
        # 查找匹配的模式
        matched_patterns = []
        for pattern in compiled_patterns:
            if pattern['regex'].search(text):
                matched_patterns.append(pattern['name'])
        
        # 判断结果
        actually_matched = len(matched_patterns) > 0
        result = "✓" if (actually_matched == should_match) else "✗"
        
        print(f"{result} {description}")
        print(f"   文本: {text}")
        print(f"   预期: {'匹配' if should_match else '不匹配'}")
        print(f"   实际: {'匹配' if actually_matched else '不匹配'}")
        
        if matched_patterns:
            print(f"   匹配的模式: {', '.join(matched_patterns)}")
        print()


def example_database_integration():
    """示例5: 数据库集成"""
    print("=" * 60)
    print("示例5: 数据库集成（模拟）")
    print("=" * 60)
    
    # 模拟数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'mass_email_dev',
        'username': 'dev_user',
        'password': 'dev_password'
    }
    
    # 生成模式
    services = [
        {
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        }
    ]
    
    all_patterns = batch_generate_all_patterns(services)
    
    print(f"准备插入 {len(all_patterns)} 个模式到数据库")
    
    # 模拟批量插入（实际使用时需要真实的数据库连接）
    try:
        # 这里只是演示API调用，实际需要数据库连接
        print("模拟数据库操作...")
        print("- 验证模式语法")
        print("- 检测冲突")
        print("- 批量插入模式")
        print("- 更新索引")
        
        # validated_count, conflicts = batch_insert_patterns_optimized(all_patterns, db_config)
        # print(f"成功插入 {validated_count} 个模式")
        # print(f"检测到 {len(conflicts)} 个冲突")
        
        print("数据库集成完成（模拟）")
        
    except Exception as e:
        print(f"数据库操作失败: {e}")


def example_performance_optimization():
    """示例6: 性能优化"""
    print("=" * 60)
    print("示例6: 性能优化")
    print("=" * 60)
    
    import time
    
    # 创建大量服务数据进行性能测试
    services = []
    for i in range(50):
        services.append({
            'id': i,
            'authoritative_full_name': f'Test Service {i} (TS{i})',
            'full_name_en': f'Test Service {i} (TS{i})',
            'short_name_en': f'Test Service {i}',
            'service_code': f'ts{i}'
        })
    
    print(f"性能测试: 为 {len(services)} 个服务生成模式")
    
    # 测试生成性能
    start_time = time.time()
    all_patterns = batch_generate_all_patterns(services)
    generation_time = time.time() - start_time
    
    print(f"生成结果:")
    print(f"  生成时间: {generation_time:.2f} 秒")
    print(f"  模式总数: {len(all_patterns)}")
    print(f"  生成速度: {len(all_patterns)/generation_time:.1f} 模式/秒")
    
    # 测试验证性能
    start_time = time.time()
    valid_count = 0
    for pattern in all_patterns:
        is_valid, _ = validate_pattern_syntax(pattern)
        if is_valid:
            valid_count += 1
    validation_time = time.time() - start_time
    
    print(f"验证结果:")
    print(f"  验证时间: {validation_time:.2f} 秒")
    print(f"  有效模式: {valid_count}")
    print(f"  验证速度: {len(all_patterns)/validation_time:.1f} 模式/秒")
    
    # 内存使用估算
    import sys
    total_size = sum(sys.getsizeof(str(pattern)) for pattern in all_patterns)
    print(f"内存使用:")
    print(f"  估算大小: {total_size/1024:.1f} KB")
    print(f"  平均每模式: {total_size/len(all_patterns):.1f} 字节")


def example_export_patterns():
    """示例7: 导出模式用于备份"""
    print("=" * 60)
    print("示例7: 导出模式用于备份")
    print("=" * 60)
    
    # 生成一些模式
    services = [
        {
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        },
        {
            'id': 2,
            'authoritative_full_name': 'Amazon Relational Database Service (RDS)',
            'full_name_en': 'Amazon Relational Database Service (RDS)',
            'short_name_en': 'Amazon RDS',
            'service_code': 'rds'
        }
    ]
    
    all_patterns = batch_generate_all_patterns(services)
    
    # 准备导出数据
    export_data = {
        'export_timestamp': datetime.now().isoformat(),
        'total_patterns': len(all_patterns),
        'services_count': len(services),
        'patterns': []
    }
    
    # 转换模式数据
    for pattern in all_patterns:
        export_pattern = {
            'pattern_name': pattern['pattern_name'],
            'pattern_type': pattern['pattern_type'],
            'regex_string': pattern['regex_string'],
            'service_code': pattern['service_code'],
            'priority': pattern['priority'],
            'notes': pattern.get('notes', ''),
            'is_active': pattern.get('is_active', True)
        }
        export_data['patterns'].append(export_pattern)
    
    # 导出到文件
    output_file = f"pattern_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"成功导出 {len(all_patterns)} 个模式到文件: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file)/1024:.1f} KB")
        
    except Exception as e:
        print(f"导出失败: {e}")


def main():
    """运行所有示例"""
    print("AWS服务模式生成系统使用示例")
    print("=" * 60)
    
    examples = [
        example_basic_pattern_generation,
        example_batch_generation,
        example_pattern_validation,
        example_pattern_matching,
        example_database_integration,
        example_performance_optimization,
        example_export_patterns
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
            print(f"\n示例 {i} 完成\n")
        except Exception as e:
            print(f"\n示例 {i} 执行失败: {e}\n")
    
    print("所有示例执行完成!")


if __name__ == "__main__":
    import os
    main()