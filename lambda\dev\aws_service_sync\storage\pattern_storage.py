"""
正则表达式模式数据库存储模块

实现新的模式生成策略与PostgreSQL数据库的完整集成，
支持批量插入、冲突检测、性能优化和模式验证。
"""

import psycopg2
import psycopg2.extras
import logging
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class PatternStorageManager:
    """正则表达式模式存储管理器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        self.db_config = db_config
        self._connection_pool = None
    
    def get_db_connection(self):
        """获取数据库连接"""
        return psycopg2.connect(
            host=self.db_config['host'],
            port=self.db_config['port'],
            database=self.db_config['database'],
            user=self.db_config['username'],
            password=self.db_config['password']
        )
    
    def ensure_pattern_table_exists(self):
        """确保正则表达式模式表存在"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS regex_patterns (
            id BIGSERIAL PRIMARY KEY,
            pattern_name VARCHAR(100) NOT NULL UNIQUE,
            pattern_type VARCHAR(50) NOT NULL,
            regex_string TEXT NOT NULL,
            related_service_id BIGINT REFERENCES service_names(id),
            service_code VARCHAR(50),
            priority INTEGER NOT NULL DEFAULT 100,
            notes TEXT,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            validation_status VARCHAR(20) DEFAULT 'pending',
            performance_score INTEGER,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
        
        -- 创建索引以优化查询性能
        CREATE INDEX IF NOT EXISTS idx_regex_patterns_priority 
            ON regex_patterns (priority DESC, id ASC);
        CREATE INDEX IF NOT EXISTS idx_regex_patterns_service 
            ON regex_patterns (related_service_id);
        CREATE INDEX IF NOT EXISTS idx_regex_patterns_type_active 
            ON regex_patterns (pattern_type, is_active);
        CREATE INDEX IF NOT EXISTS idx_regex_patterns_service_code 
            ON regex_patterns (service_code);
        CREATE INDEX IF NOT EXISTS idx_regex_patterns_name 
            ON regex_patterns (pattern_name);
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(create_table_sql)
                    conn.commit()
            logger.info("Pattern table and indexes created/verified successfully")
        except Exception as e:
            logger.error(f"Failed to create pattern table: {e}")
            raise
    
    def batch_insert_patterns_optimized(self, patterns: List[Dict[str, Any]]) -> Tuple[int, List[Dict[str, Any]]]:
        """优化的批量模式插入"""
        if not patterns:
            return 0, []
        
        # 验证所有模式
        validated_patterns = []
        validation_errors = []
        
        for pattern in patterns:
            is_valid, error = self._validate_pattern_syntax(pattern)
            if is_valid:
                pattern['validation_status'] = 'valid'
                validated_patterns.append(pattern)
            else:
                logger.warning(f"Invalid pattern {pattern['pattern_name']}: {error}")
                pattern['validation_status'] = 'invalid'
                validation_errors.append({
                    'pattern_name': pattern['pattern_name'],
                    'error': error
                })
        
        # 检测冲突
        conflicts = self._detect_pattern_conflicts(validated_patterns)
        if conflicts:
            logger.warning(f"Detected {len(conflicts)} pattern conflicts")
            for conflict in conflicts:
                logger.warning(f"Priority {conflict['priority']} conflict: {conflict['patterns']}")
        
        # 批量插入到数据库
        inserted_count = 0
        if validated_patterns:
            inserted_count = self._execute_batch_insert(validated_patterns)
        
        return inserted_count, conflicts
    
    def _validate_pattern_syntax(self, pattern: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """验证正则表达式模式的语法正确性"""
        import re
        try:
            re.compile(pattern['regex_string'], re.IGNORECASE)
            return True, None
        except re.error as e:
            return False, str(e)
    
    def _detect_pattern_conflicts(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检测模式之间的潜在冲突"""
        conflicts = []
        
        # 检查优先级冲突
        priority_groups = {}
        for pattern in patterns:
            priority = pattern['priority']
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(pattern)
        
        for priority, group in priority_groups.items():
            if len(group) > 1:
                conflicts.append({
                    'type': 'priority_conflict',
                    'patterns': [p['pattern_name'] for p in group],
                    'priority': priority,
                    'count': len(group)
                })
        
        # 检查模式名称重复
        name_groups = {}
        for pattern in patterns:
            name = pattern['pattern_name']
            if name not in name_groups:
                name_groups[name] = []
            name_groups[name].append(pattern)
        
        for name, group in name_groups.items():
            if len(group) > 1:
                conflicts.append({
                    'type': 'name_conflict',
                    'patterns': [name],
                    'count': len(group)
                })
        
        return conflicts
    
    def _execute_batch_insert(self, patterns: List[Dict[str, Any]]) -> int:
        """执行批量插入操作"""
        insert_sql = """
        INSERT INTO regex_patterns 
            (pattern_name, pattern_type, regex_string, related_service_id, 
             service_code, priority, notes, validation_status, is_active)
        VALUES %s
        ON CONFLICT (pattern_name) 
        DO UPDATE SET
            regex_string = EXCLUDED.regex_string,
            priority = EXCLUDED.priority,
            notes = EXCLUDED.notes,
            validation_status = EXCLUDED.validation_status,
            updated_at = NOW()
        """
        
        # 准备数据
        pattern_data = []
        for pattern in patterns:
            pattern_data.append((
                pattern['pattern_name'],
                pattern['pattern_type'],
                pattern['regex_string'],
                pattern.get('related_service_id'),
                pattern.get('service_code'),
                pattern['priority'],
                pattern.get('notes', ''),
                pattern.get('validation_status', 'valid'),
                pattern.get('is_active', True)
            ))
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    psycopg2.extras.execute_values(
                        cur, insert_sql, pattern_data, 
                        template=None, page_size=100
                    )
                    conn.commit()
                    inserted_count = cur.rowcount
            
            logger.info(f"Successfully inserted/updated {inserted_count} patterns")
            return inserted_count
            
        except Exception as e:
            logger.error(f"Failed to insert patterns: {e}")
            raise
    
    def get_active_patterns_by_priority(self, pattern_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """按优先级获取活跃的模式"""
        base_sql = """
        SELECT 
            id, pattern_name, pattern_type, regex_string, 
            related_service_id, service_code, priority, notes,
            is_active, validation_status, performance_score,
            created_at, updated_at
        FROM regex_patterns 
        WHERE is_active = true
        """
        
        params = []
        if pattern_type:
            base_sql += " AND pattern_type = %s"
            params.append(pattern_type)
        
        base_sql += " ORDER BY priority DESC, id ASC"
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                    cur.execute(base_sql, params)
                    patterns = cur.fetchall()
            
            # 转换为字典列表
            return [dict(pattern) for pattern in patterns]
            
        except Exception as e:
            logger.error(f"Failed to fetch patterns: {e}")
            raise
    
    def update_pattern_performance(self, pattern_name: str, performance_score: int):
        """更新模式性能评分"""
        update_sql = """
        UPDATE regex_patterns 
        SET performance_score = %s, updated_at = NOW()
        WHERE pattern_name = %s
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(update_sql, (performance_score, pattern_name))
                    conn.commit()
                    
            logger.debug(f"Updated performance score for pattern {pattern_name}: {performance_score}")
            
        except Exception as e:
            logger.error(f"Failed to update pattern performance: {e}")
            raise
    
    def deactivate_patterns_by_service(self, service_id: int):
        """停用指定服务的所有模式"""
        update_sql = """
        UPDATE regex_patterns 
        SET is_active = false, updated_at = NOW()
        WHERE related_service_id = %s
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(update_sql, (service_id,))
                    conn.commit()
                    affected_rows = cur.rowcount
                    
            logger.info(f"Deactivated {affected_rows} patterns for service {service_id}")
            return affected_rows
            
        except Exception as e:
            logger.error(f"Failed to deactivate patterns: {e}")
            raise
    
    def cleanup_invalid_patterns(self):
        """清理无效的模式"""
        delete_sql = """
        DELETE FROM regex_patterns 
        WHERE validation_status = 'invalid' 
        AND created_at < NOW() - INTERVAL '7 days'
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(delete_sql)
                    conn.commit()
                    deleted_count = cur.rowcount
                    
            logger.info(f"Cleaned up {deleted_count} invalid patterns")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup invalid patterns: {e}")
            raise
    
    def get_pattern_statistics(self) -> Dict[str, Any]:
        """获取模式统计信息"""
        stats_sql = """
        SELECT 
            COUNT(*) as total_patterns,
            COUNT(*) FILTER (WHERE is_active = true) as active_patterns,
            COUNT(*) FILTER (WHERE validation_status = 'valid') as valid_patterns,
            COUNT(*) FILTER (WHERE validation_status = 'invalid') as invalid_patterns,
            COUNT(DISTINCT pattern_type) as pattern_types,
            COUNT(DISTINCT service_code) as services_covered,
            AVG(performance_score) FILTER (WHERE performance_score IS NOT NULL) as avg_performance,
            MAX(priority) as max_priority,
            MIN(priority) as min_priority
        FROM regex_patterns
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                    cur.execute(stats_sql)
                    stats = cur.fetchone()
            
            return dict(stats) if stats else {}
            
        except Exception as e:
            logger.error(f"Failed to get pattern statistics: {e}")
            raise
    
    def export_patterns_for_backup(self, output_file: str):
        """导出模式用于备份"""
        export_sql = """
        SELECT 
            pattern_name, pattern_type, regex_string, service_code,
            priority, notes, is_active, validation_status
        FROM regex_patterns 
        ORDER BY priority DESC, pattern_name
        """
        
        try:
            with self.get_db_connection() as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                    cur.execute(export_sql)
                    patterns = cur.fetchall()
            
            # 转换为JSON格式并保存
            patterns_data = [dict(pattern) for pattern in patterns]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_timestamp': datetime.now().isoformat(),
                    'total_patterns': len(patterns_data),
                    'patterns': patterns_data
                }, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Exported {len(patterns_data)} patterns to {output_file}")
            return len(patterns_data)
            
        except Exception as e:
            logger.error(f"Failed to export patterns: {e}")
            raise


def create_pattern_storage_manager(db_config: Dict[str, Any]) -> PatternStorageManager:
    """创建模式存储管理器实例"""
    manager = PatternStorageManager(db_config)
    manager.ensure_pattern_table_exists()
    return manager


# 集成函数，供主系统调用
def batch_insert_patterns_optimized(patterns: List[Dict[str, Any]], db_config: Dict[str, Any]) -> Tuple[int, List[Dict[str, Any]]]:
    """批量插入模式的集成函数"""
    manager = create_pattern_storage_manager(db_config)
    return manager.batch_insert_patterns_optimized(patterns)


def get_active_service_patterns(db_config: Dict[str, Any], pattern_type: str = 'SERVICE_NAME') -> List[Dict[str, Any]]:
    """获取活跃服务模式的集成函数"""
    manager = create_pattern_storage_manager(db_config)
    return manager.get_active_patterns_by_priority(pattern_type)


# 使用示例
if __name__ == "__main__":
    # 示例配置
    sample_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'mass_email_dev',
        'username': 'dev_user',
        'password': 'dev_password'
    }
    
    # 创建管理器
    manager = create_pattern_storage_manager(sample_config)
    
    # 示例模式数据
    sample_patterns = [
        {
            'pattern_name': 'EC2_FULL_STANDARD',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)(?<!https?://[^\s]*)\bAmazon\s+Elastic\s+Compute\s+Cloud\s*\(EC2\)\b(?![:_-])(?![^\s]*\.[a-z]{2,4})',
            'service_code': 'ec2',
            'priority': 115,
            'notes': 'Standard full name pattern with boundary protection'
        }
    ]
    
    # 批量插入
    inserted_count, conflicts = manager.batch_insert_patterns_optimized(sample_patterns)
    print(f"Inserted {inserted_count} patterns with {len(conflicts)} conflicts")
    
    # 获取统计信息
    stats = manager.get_pattern_statistics()
    print(f"Pattern statistics: {stats}")