20250609 - 
1. 关于global的url链接替换，对应的中国区doc没有的话，需要增加对替换后的页面访问的逻辑，验证替换后的链接访问是否正常，如果有问题会标识出来。
2. 链接中 repost，aws blog类型的链接不做替换保留输出供人工核对，但是输出时进行标识。
3. "Regularly update the LLM-assisted service name dictionary to include new services or newly identified variants of existing service names."
   三种更新场景： 1. feedback ，这个是实时的，问题修复； 2. 我主动发现的，这个也是实时的；3. 我计划半年对这个页面进行扫描确认新的服务名称：https://www.amazonaws.cn/en/about-aws/regional-product-services/