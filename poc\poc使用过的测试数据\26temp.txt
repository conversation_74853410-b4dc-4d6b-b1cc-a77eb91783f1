样例1:
英文原文:
'
Hello,

On {{properties.first_notification_sent.value}}, we emailed you about Amazon EC2 {{properties.instance_family.value}} instances reaching end of life on {{properties.eol_date.value}}. EC2 has detected an active {{properties.instance_family.value}} instance (instance-ID: {{properties.instance_id.value}}) associated with your AWS account (AWS Account ID: {{accountEntities.[0].accountId}}) in the {{region}} Region. You need to migrate to a newer generation instance type no later than {{properties.start_time.value}}. Amazon EC2 instance (instance-ID: {{properties.instance_id.value}}) will be stopped on {{properties.start_time.value}}. You will not be able to restart your stopped instance without modifying the instance type as the {{properties.instance_family.value}} instance type configuration is no longer supported.

A list of one or more active {{properties.instance_family.value}} instances can be found in the "Affected Resources" tab of your AWS Health Dashboard.

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the {{properties.instance_family.value}} instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new {{properties.instance_family.value}} instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the EBS volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the EBS volume(s) associated with the stopped instance, you can either delete the EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the {{properties.instance_family.value}} instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. {{properties.instance_family.value}} instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the {{properties.instance_family.value}} instance family. Newer generation instances feature the AWS Nitro System and typically offer better price-performance than {{properties.instance_family.value}} instances.

If you have any questions or concerns, you can reach out to your AWS Accounts Teams or the AWS Support Team [1].

[1] https://aws.amazon.com/support
'

替换后的英文:
'
Hello,

On {{properties.first_notification_sent.value}}, we emailed you about Amazon Elastic Compute Cloud (Amazon EC2) {{properties.instance_family.value}} instances reaching end of life on {{properties.eol_date.value}}. Amazon EC2 has detected an active {{properties.instance_family.value}} instance (instance-ID: {{properties.instance_id.value}}) associated with your Amazon Web Services account (Account ID: {{accountEntities.[0].accountId}}) in the {{region}} Region. You need to migrate to a newer generation instance type no later than {{properties.start_time.value}}. Amazon EC2 instance (instance-ID: {{properties.instance_id.value}}) will be stopped on {{properties.start_time.value}}. You will not be able to restart your stopped instance without modifying the instance type as the {{properties.instance_family.value}} instance type configuration is no longer supported.

A list of one or more active {{properties.instance_family.value}} instances can be found in the "Affected Resources" tab of your Amazon Health Dashboard.

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the {{properties.instance_family.value}} instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new {{properties.instance_family.value}} instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the Amazon Elastic Block Store (Amazon EBS) volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach Amazon EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the Amazon EBS volume(s) associated with the stopped instance, you can either delete the Amazon EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the {{properties.instance_family.value}} instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. {{properties.instance_family.value}} instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the {{properties.instance_family.value}} instance family. Newer generation instances feature the Amazon Nitro System and typically offer better price-performance than {{properties.instance_family.value}} instances.

If you have any questions or concerns, please contact Amazon Web Services China Support [1].

[1] https://console.amazonaws.cn/support/
'

翻译示例:
'
在{{metadata.first_notification_sent.value}}，我们通过电子邮件通知您，Amazon Elastic Compute Cloud (Amazon EC2)的{{metadata.instance_family.value}}实例将在{{metadata.eol_date.value}}停止服务。Amazon EC2检测到活跃的{{metadata.instance_family.value}}实例（实例ID：{{metadata.instance_id.value}}）位于{{region}}区域。您需要在{{metadata.start_time.value}}之前迁移到新一代实例类型。Amazon EC2实例（实例ID：{{metadata.instance_id.value}}）将在{{metadata.start_time.value}}停止。由于{{metadata.instance_family.value}}实例类型配置不再受支持，您在未修改实例类型的情况下将无法重新启动已停止的实例。

您可以在“受影响的资源”选项卡中找到一或多个活跃的{{metadata.instance_family.value}}实例的列表。

我的实例在停止后将发生什么？
您的实例将在指定日期停止。由于{{metadata.instance_family.value}}实例类型不再受支持，您在未修改实例类型的情况下将无法重新启动已停止的实例。此外，当实例停止时，任何本地实例存储卷上的数据将不会被保留。您也将无法启动新的{{metadata.instance_family.value}}实例。

我需要做些什么？
您必须在实例停止的指定日期之前迁移到新一代实例类型。新一代实例通常比旧一代实例提供更好的性价比。

实例停止后，附加到实例的Amazon Elastic Block Store (Amazon EBS)卷和ENI会发生什么？
即使实例已停止，您也可以分离与实例关联的Amazon EBS卷。为了停止对与已停止实例关联的Amazon EBS卷计费，您可以在从实例分离后删除Amazon EBS卷，或者为与您的实例关联的EBS卷启用“删除时终止”标志并终止您的实例。实例停止后，您可以分离与实例关联的任何次要ENI。为了能够将主网络接口用于另一个实例，您需要禁用主ENI的“删除时终止”标志并终止实例。

为什么不再支持{{metadata.instance_family.value}}实例？
Amazon EC2为客户提供可扩展和可靠的体验。由于我们无法继续在{{metadata.instance_family.value}}实例系列上提供您期望的高水平弹性，因此不再支持该实例系列。新一代实例配备了Amazon Nitro系统，并且通常比{{metadata.instance_family.value}}实例提供更好的性价比。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [1]。

[1] https://console.amazonaws.cn/support/
'


样例2:
英文原文:
'
Service: TRANSIT_GATEWAY
Region:  BJS | ZHY   
Failure mode 1:
TypeCode: AWS_TRANSIT_GATEWAY_TRAVERSING_PACKET_LOSS_ISSUE
Wording: We are investigating potential packet loss for traffic traversing your Transit Gateway in the {{region}} Region.
'

替换后的英文:
'
Service: TRANSIT_GATEWAY
Region:  BJS | ZHY   
Failure mode 1:
TypeCode: AWS_TRANSIT_GATEWAY_TRAVERSING_PACKET_LOSS_ISSUE
Wording: We are investigating potential packet loss for traffic traversing your Transit Gateway in the {{region}} Region.
'

翻译示例:
'
Service: TRANSIT_GATEWAY
Region:  BJS | ZHY   
Failure mode 1:
TypeCode: AWS_TRANSIT_GATEWAY_TRAVERSING_PACKET_LOSS_ISSUE
Wording : 我们正在调查{{region}}区域内通过您Amazon Transit Gateway的流量可能丢包的情况。
'

样例3:
英文原文:
'
We identified you have Amazon SageMaker Notebook instances in your account which need to be restarted to receive important security updates. To receive these updates, you must stop [1] and start [2] your Notebook Instance(s) through the AWS Management Console or using the AWS SDK. Restarting the affected Notebook Instance(s) will automatically apply the necessary software updates [3]. 

A list of your affected Notebook Instances is available in the "Affected resources" tab of your AWS Health Dashboard. If any of these resources have been deleted or no longer exist in the corresponding AWS account, you do not need to take any further action for that resource.

If you have any questions or concerns, please contact AWS Support [4].

[1] https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_StopNotebookInstance.html
[2] https://docs.aws.amazon.com/sagemaker/latest/APIReference/API_StartNotebookInstance.html
[3] https://docs.aws.amazon.com/sagemaker/latest/dg/nbi-software-updates.html
[4] https://aws.amazon.com/support
'

替换后的英文:
'
We identified you have Amazon SageMaker Notebook instances in your account which need to be restarted to receive important security updates. To receive these updates, you must stop [1] and start [2] your Notebook Instance(s) through the Amazon Web Services Management Console or using the Amazon SDK. Restarting the affected Notebook Instance(s) will automatically apply the necessary software updates [3].  

A list of your affected Notebook Instances is available in the "Affected resources" tab. If any of these resources have been deleted or no longer exist in the corresponding Amazon Web Services account, you do not need to take any further action for that resource.

If you have any questions or concerns, please contact Amazon Web Services China Support [4].

[1] https://docs.amazonaws.cn/en_us/sagemaker/latest/APIReference/API_StopNotebookInstance.html
[2] https://docs.amazonaws.cn/en_us/sagemaker/latest/APIReference/API_StartNotebookInstance.html
[3] https://docs.amazonaws.cn/en_us/sagemaker/latest/dg/nbi-software-updates.html
[4] https://console.amazonaws.cn/support
'

翻译示例:
'
我们发现您的账户中有 Amazon SageMaker 笔记本实例，可能需要重新启动这些实例才能接收重要的安全更新。要接收这些更新，您必须通过亚马逊云科技管理控制台或使用 Amazon SDK 停止 [1] 并启动 [2] 您的笔记本实例。重启受影响的笔记本实例将自动应用必要的软件更新 [3]。

您可以在 “受影响的资源” 选项卡中找到受影响的笔记本实例列表。如果这些资源中的任何资源已被删除或不再存在于相应的亚马逊云科技账户中，则无需对该资源采取任何进一步措施。

如果您有任何问题或者疑虑，请联系亚马逊云科技中国支持团队 [4]。

[1] https://docs.amazonaws.cn/sagemaker/latest/APIReference/API_StopNotebookInstance.html
[2] https://docs.amazonaws.cn/sagemaker/latest/APIReference/API_StartNotebookInstance.html
[3] https://docs.amazonaws.cn/sagemaker/latest/dg/nbi-software-updates.html
[4] https://console.amazonaws.cn/support

'

样例4:
英文原文:
'
We are reaching out to remind you to take actions specified in a prior notification, entitled "[Action Required] Amazon EC2 P2 Discontinuation Notice" that we sent in March 2024.

We first launched EC2 P2 instances ("P2 instances") in September 2016 and have continued to provide customers a scalable AWS experience on P2 instances since then. However, we can no longer continue to offer the high level of elasticity you have come to expect on the P2 instance family and are therefore working with customers to migrate their workloads to newer G-series or P-series instances by March 31, 2025. We recommend customers to migrate to newer G-series or newer P-series instances. Existing users of P2 instances will see no changes in their experience leading up to the retirement date on March 31, 2025. However, customers are no longer able to purchase new Reserved Instances or Instance Savings Plans for P2 instances. To learn more about these instances, you can refer to the AWS documentation [1].

Starting April 1, 2025, customers will no longer be able to launch new P2 instances or restart stopped P2 instances without modifying the instance type. By April 1, 2025, you will receive a notification if you have any running P2 instances, informing you that P2 instances will be stopped on May 1, 2025.

Please reach out to your AWS Account Team or AWS Support [2] for any questions.

FAQs: 

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the P2 instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new P2 instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the EBS volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the EBS volume(s) associated with the stopped instance, you can either delete the EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the P2 instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. The P2 instance type is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the P2 instance family. Newer generation instances feature the AWS Nitro System and typically offer better price-performance than P2 instances.

[1] https://aws.amazon.com/ec2/instance-types/#Accelerated_Computing
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We are reaching out to remind you to take actions specified in a prior notification, entitled "[Action Required] Amazon EC2 P2 Discontinuation Notice" that we sent in April 2024.

We first launched Amazon Elastic Compute Cloud (EC2) P2 instances ("P2 instances") in September 2016 and have continued to provide customers a scalable Amazon Web Services experience on P2 instances since then. However, we can no longer continue to offer the high level of elasticity you have come to expect on the P2 instance family and are therefore working with customers to migrate their workloads to newer G-series or P-series instances by March 31, 2025. We recommend customers to migrate to newer G-series or newer P-series instances. Existing users of P2 instances will see no changes in their experience leading up to the retirement date on March 31, 2025. However, customers are no longer able to purchase new Reserved Instances or Instance Savings Plans for P2 instances. To learn more about these instances, you can refer to the Amazon Web Services documentation [1].

Starting April 1, 2025, customers will no longer be able to launch new P2 instances or restart stopped P2 instances without modifying the instance type. By April 1, 2025, you will receive a notification if you have any running P2 instances, informing you that P2 instances will be stopped on May 1, 2025.

If you have any questions or concerns, please contact Amazon Web Services China Support [2].

FAQs: 

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the P2 instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new P2 instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the  Amazon Elastic Block Store (EBS) volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach Amazon EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the EBS volume(s) associated with the stopped instance, you can either delete the EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the P2 instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. The P2 instance type is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the P2 instance family. Newer generation instances feature the Amazon Nitro System and typically offer better price-performance than P2 instances.

[1] https://www.amazonaws.cn/en/ec2/instance-types/#Accelerated_Computing
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
我们联系您是为了提醒您针对之前发送的通知采取指定的行动，通知标题为"[需要操作] Amazon EC2 P2机型停用通知"，该通知已于2024年4月发送。

我们于 2016 年 9 月首次推出 Amazon Elastic Compute Cloud (EC2) P2 实例（以下简称"P2 实例"），此后一直在 P2 实例上为客户提供可扩展的亚马逊云科技体验。 但是，我们无法继续在 P2 实例系列上提供您所期望的高弹性，因此我们正在与客户合作，在 2025 年 3 月 31 日之前将其工作负载迁移到较新的 G 系列或 P 系列实例。 我们建议客户迁移到较新的 G 系列或 P 系列实例。现有的 P2 实例用户在截至2025年3月31日的退役日期之前，使用体验不会发生任何变化。然而，客户将无法再购买新的 P2 预留实例或实例节省计划。要了解有关这些实例的更多信息，您可以参考亚马逊云科技文档 [1]。

自2025年4月1日起，客户将无法启动新的 P2 实例或在未修改实例类型的情况下重新启动已停止的 P2 实例。到2025年4月1日，如果您有任何正在运行的 P2 实例，您将收到通知，告知您 P2 实例将于2025年5月1日停止运行。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [2]。

常见问题解答：

* 我的实例停止后会发生什么？
您的实例将在指定日期停止。由于 P2 实例类型不再受支持，实例停止后，您将无法在不修改实例类型的情况下重新启动实例。此外，当实例停止时，任何实例存储卷上的数据将不会被保留。您也无法启动新的 P2 实例。

* 我需要做什么？
您必须在实例停止的指定日期之前迁移到更新一代的实例类型。新一代实例通常比旧一代实例提供更好的性价比。

* 实例停止后，附加到实例的 Amazon Elastic Block Store (EBS) 卷和 ENI(s) 会发生什么？
即使实例已停止，您仍然可以分离与实例关联的 Amazon EBS 卷。要停止对已停止实例关联的Amazon EBS 卷的计费，您可以在从实例分离Amazon EBS 卷后删除它，或者为实例关联的Amazon EBS 卷启用“终止时删除”标志并终止您的实例。实例停止后，您可以分离任何关联的辅助 ENI。您需要禁用主 ENI 的“终止时删除”标志并终止实例，才能将主网络接口用于其他实例。

* 为什么 P2 实例不再受支持？
Amazon EC2 致力于为客户提供可扩展且可靠的体验。P2 实例类型不再受支持是因为我们无法继续在 P2 实例系列上提供您期望的高弹性。新一代实例采用Amazon Nitro 系统，通常比 P2 实例提供更好的性价比。

[1] https://www.amazonaws.cn/ec2/instance-types/#Accelerated_Computing
[2] https://console.amazonaws.cn/support
'


样例5:
英文原文:
'
[Amazon Health Dashboard may periodically trigger reminder notifications about this communication]

We are contacting you as we have identified that your AWS Account currently has one or more AWS Lambda functions using the Node.js 16 runtime.

We ended support for Node.js 16 in Lambda on June 12, 2024. This follows Node.js 16 End-Of-Life (EOL) reached on September 11, 2023 [1].

As described in the Lambda runtime support policy [2], end of support for language runtimes in Lambda happens in several stages. Starting on June 12, 2024, Lambda will no longer apply security patches and other updates to the Node.js 16 runtime used by Lambda functions, and functions using Node.js 16 will no longer be eligible for technical support. Also, Node.js 16 will no longer be available in the AWS Console, although you can still create and update functions that use Node.js 16 via AWS CloudFormation, the AWS CLI, AWS SAM, or other tools. Starting October 1, 2025, you will no longer be able to create new Lambda functions using the Node.js 16 runtime. Starting November 1, 2025, you will no longer be able to update existing functions using the Node.js 16 runtime.

We recommended that you upgrade your existing Node.js 16 functions to the latest available Node.js runtime as soon as possible. A list of your Node.js 16 functions can be found in the 'Affected resources' tab of the AWS Health Dashboard.

End of support does not impact function execution. Your functions will continue to run. However, they will be running on an unsupported runtime which is no longer maintained or patched by the AWS Lambda team.

This notification is generated for functions using the Node.js 16 runtime for the $LATEST function version. The following command shows how to use the AWS CLI [3] to list all functions in a specific region using Node.js 16, including published function versions. To find all such functions in your account, repeat this command for each region:

aws lambda list-functions --region us-west-2 --output text --query "Functions[?Runtime=='nodejs16.x'].FunctionArn"

From 180 days before deprecation, you can also use Trusted Advisor to identify all functions using the Node.js 16 runtime, including published function versions [4].

If you have any concerns or require further assistance, please contact AWS Support [5].

[1] https://github.com/nodejs/Release
[2] https://docs.aws.amazon.com/lambda/latest/dg/runtime-support-policy.html
[3] https://aws.amazon.com/cli/
[4] https://docs.aws.amazon.com/awssupport/latest/user/security-checks.html#aws-lambda-functions-deprecated-runtimes
[5] https://aws.amazon.com/support
'

替换后的英文:
'

[Amazon Health Dashboard may periodically trigger reminder notifications about this communication]

We are contacting you as we have identified that your Amazon Web Services Account currently has one or more Amazon Lambda functions using the Node.js 16 runtime.

We ended support for Node.js 16 in Lambda on June 12, 2024. This follows Node.js 16 End-Of-Life (EOL) reached on September 11, 2023 [1].

As described in the Lambda runtime support policy [2], end of support for language runtimes in Lambda happens in several stages. Starting on June 12, 2024, Lambda will no longer apply security patches and other updates to the Node.js 16 runtime used by Lambda functions, and functions using Node.js 16 will no longer be eligible for technical support. Also, Node.js 16 will no longer be available in the Amazon Web Services Console, although you can still create and update functions that use Node.js 16 via Amazon CloudFormation, the Amazon Command Line Interface (Amazon CLI), Serverless Application Model (Amazon SAM), or other tools. Starting October 1, 2025, you will no longer be able to create new Lambda functions using the Node.js 16 runtime. Starting November 1, 2025, you will no longer be able to update existing functions using the Node.js 16 runtime.

We recommended that you upgrade your existing Node.js 16 functions to the latest available Node.js runtime as soon as possible. A list of your Node.js 16 functions can be found in the 'Affected resources' tab.

End of support does not impact function execution. Your functions will continue to run. However, they will be running on an unsupported runtime which is no longer maintained or patched by the Amazon Lambda team.

This notification is generated for functions using the Node.js 16 runtime for the $LATEST function version. The following command shows how to use the Amazon CLI [3] to list all functions in a specific region using Node.js 16, including published function versions. To find all such functions in your account, please use the following command:

aws lambda list-functions --function-version ALL --region cn-northwest-1 --output json --query "Functions[?Runtime=='nodejs16.x'].FunctionArn"

If you have any questions or concerns, please contact Amazon Web Services China Support [4].

[1] https://github.com/nodejs/Release
[2] https://docs.amazonaws.cn/en_us/lambda/latest/dg/lambda-runtimes.html#runtime-support-policy
[3] https://www.amazonaws.cn/en/tools/ 
[4] https://console.amazonaws.cn/support
'

翻译示例:
'
[Amazon Health Dashboard 可能会定期触发关于此通知的提醒]

我们联系您是因为我们发现您的亚马逊云科技账户当前存在一个或多个使用 Node.js 16 运行时的 Amazon Lambda 函数。

我们已于2024年6月12日停止了对 Lambda 中 Node.js 16 的支持。这是因为 Node.js 16 的生命周期已于2023年9月11日结束[1]。

正如 Lambda 运行时支持策略[2]中所描述的，Lambda 对语言运行时的支持终止分为几个阶段。从2024年6月12日起，Lambda 将不再为使用 Node.js 16 运行时的 Lambda 函数提供安全补丁和其他更新，并且这些函数将不再享有技术支持。此外，亚马逊云科技控制台将不再提供Node.js 16 ，但您仍可以通过 Amazon CloudFormation、Amazon Command Line Interface (Amazon CLI)、Serverless Application Model (Amazon SAM) 或其他工具创建和更新使用 Node.js 16 的函数。自2025年10月1日起，您将无法使用 Node.js 16 运行时创建新的 Lambda 函数。自2025年11月1日起，您也将无法使用 Node.js 16 运行时更新现有函数。

我们建议您尽快将现有的 Node.js 16 函数升级到最新可用的 Node.js 运行时。您可以在“受影响的资源”选项卡中找到所有 Node.js 16 函数的列表。

结束支持不会影响函数的执行，您的函数将继续运行，但是，它们将在不受支持的运行时上运行，Amazon Lambda 团队不再维护或修补该运行时。

此通知针对使用 Node.js 16 运行时的 $LATEST 函数版本生成。以下命令展示了如何使用 Amazon CLI[3]列出特定区域内所有使用 Node.js 16 的函数（包括已发布的函数版本）。要查找您账户中所有此类函数，请使用以下命令：

aws lambda list-functions --function-version ALL --region cn-northwest-1 --output json --query "Functions[?Runtime=='nodejs16.x'].FunctionArn"

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [4]。

[1] https://github.com/nodejs/Release
[2] https://docs.amazonaws.cn/lambda/latest/dg/lambda-runtimes.html#runtime-support-policy
[3] https://www.amazonaws.cn/tools/
[4] https://console.amazonaws.cn/support
'


样例6:
英文原文:
'
This is a follow-up to our previous message. Beginning March 17, 2025, we are making a change to Amazon Simple Queue Service (Amazon SQS) queue access policy validation, which requires your action. Amazon SQS provides managed message queuing for micro-services, distributed systems, and Serverless applications. After this date, you must specify a valid resource when adding or updating access policies on new queues. Adding or updating an access policy without a resource or with an invalid resource ARN will fail with MalformedPolicyDocument error. We identified your account has malformed access policies attached to queues which are missing a resource specification, or have an invalid resource identifier (ARN). To provide you time to review and update access policies, we will add queues created in your account before March 17, 2025 to an allow list. If you do not take action by September 15, 2025, updating an access policy without a resource or with an invalid resource ARN will fail with MalformedPolicyDocument error.  

Please refer to the "Affected resources" tab of your AWS Health Dashboard for a list of queues in your account.

We recommend that you update your workflows that apply queue access policy through infrastructure-as-code services like AWS CDK, or through AWS SDK, console, or CLI, to include one or more valid resource ARNs. For examples of valid access policies, refer to our “Basic examples of Amazon SQS policies” user guide [1]. To configure or update access policies on queues, refer to our “Configuring an access policy in Amazon SQS” user guide [2].

If you have any questions or concerns, please contact AWS Support [3].

[1] https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-basic-examples-of-sqs-policies.html
[2] https://docs.aws.amazon.com/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-configure-add-permissions.html
[3] https://aws.amazon.com/support
'

替换后的英文:
'
This is a follow-up to our previous message. Beginning March 17, 2025, we are making a change to Amazon Simple Queue Service (Amazon SQS) queue access policy validation, which requires your action. Amazon SQS provides managed message queuing for micro-services, distributed systems, and Serverless applications. After this change, you must specify a valid resource when adding or updating access policies on new queues. Adding or updating an access policy without a resource or with an invalid resource ARN will fail with MalformedPolicyDocument error. We identified your account has malformed access policies attached to queues which are missing a resource specification, or have an invalid resource identifier (ARN). To provide you time to review and update access policies, we will add queues created in your account before March 17, 2025 to an allow list. If you do not take action by September 15, 2025, updating an access policy without a resource or with an invalid resource ARN will fail with MalformedPolicyDocument error.  

Please refer to the "Affected resources" tab for a list of queues in your account.

We recommend that you update your workflows that apply queue access policy through infrastructure-as-code services like Amazon Cloud Development Kit (Amazon CDK), or through Amazon SDK, Amazon Web Services console, or Amazon Command Line Interface (Amazon CLI), to include one or more valid resource ARNs. For examples of valid access policies, refer to our “Basic examples of Amazon SQS policies” user guide [1]. To configure or update access policies on queues, refer to our “Configuring an access policy in Amazon SQS” user guide [2].

If you have any questions or concerns, please reach out to Amazon Web Services China Support [3].

[1] https://docs.amazonaws.cn/en_us/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-basic-examples-of-sqs-policies.html 
[2] https://docs.amazonaws.cn/en_us/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-configure-add-permissions.html
[3] https://console.amazonaws.cn/support/
'

翻译示例:
'
这是我们此前消息的后续通知。从 2025 年 3 月 17 日起，我们将对 Amazon Simple Queue Service (Amazon SQS) 队列访问策略验证进行更改，这需要您采取相应措施。Amazon SQS 为微服务、分布式系统和无服务器应用程序提供托管消息队列。在此更改之后，当您在新队列上添加或更新访问策略时，必须指定有效的资源。如果添加或更新的访问策略未指定资源，或者资源ARN无效，将导致MalformedPolicyDocument 错误。我们发现您的账户中存在一些队列，其附加的访问策略存在格式错误，这些队列要么缺少资源规范，要么资源标识符（ARN）无效。为了给您时间审查和更新这些访问策略，我们将把您账户中 2025 年 3 月 17 日之前创建的队列添加到允许列表中。如果您在 2025 年 9 月 15 日之前不采取行动，在没有指定资源或使用无效资源ARN的情况下更新访问策略，将导致MalformedPolicyDocument 错误。

您可以在 “受影响资源 ”选项卡中查看有关受影响队列的列表。

我们建议您通过基础架构即代码服务, 例如 Amazon Cloud Development Kit (Amazon CDK) 或通过 Amazon SDK、亚马逊云科技管理控制台或者 Amazon Command Line Interface (Amazon CLI) 来更新您的的工作流以应用队列访问策略，并确保包含一个或多个有效的资源 ARN。有关有效访问策略的示例，请参阅我们的 “Amazon SQS 策略基本示例 ”用户指南 [1]。要配置或更新队列上的访问策略，请参阅我们的 “配置访问策略 ”用户指南 [2]。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [3]。

[1] https://docs.amazonaws.cn/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-basic-examples-of-sqs-policies.html
[2] https://docs.amazonaws.cn/AWSSimpleQueueService/latest/SQSDeveloperGuide/sqs-configure-add-permissions.html
[3] https://console.amazonaws.cn/support/
'


样例7:
英文原文:
'
We are deploying operating system maintenance updates to the underlying software for your Aurora PostgreSQL Global Database replication infrastructure. During this maintenance activity, the instances in your primary cluster will not be impacted and can continue to serve requests from your applications. However, the update requires restarting all secondary cluster instances, which will cause temporary unavailability of read replicas in the secondary region. These restarts typically complete in under a minute.

The updates and resulting restarts will occur during your secondary database cluster's maintenance window. The maintenance activities will begin on March 25, 2025, and conclude by April 13, 2025. Please note that notifications for this maintenance activity will not be viewable on the AWS RDS Console or through API/CLI.

To minimize disruption to secondary clusters in the future, we recommend upgrading to one of the following Aurora PostgreSQL engine versions: 16.6, 15.10, 14.15, 13.18, 12.22, or higher. These versions include enhancements that prevent read replica restarts during mandatory operating system updates for the Global Database replication infrastructure [1].

You can find a list of your impacted clusters in the 'Affected Resources' tab of your AWS Health Dashboard. Please note that resources created after this notification may not appear in the 'Affected Resources' tab.

If you have any questions or concerns, please reach out to AWS Support [2].

[1] https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/aurora-global-database-secondary-availability.html
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We are deploying operating system maintenance updates to the underlying software for your Amazon Aurora - PostgreSQL-compatible Global Database replication infrastructure. During this maintenance activity, the instances in your primary cluster will not be impacted and can continue to serve requests from your applications. However, the update requires restarting all secondary cluster instances, which will cause temporary unavailability of read replicas in the secondary region. These restarts typically complete in under a minute.

The updates and resulting restarts will occur during your secondary database cluster's maintenance window. The maintenance activities will begin on March 25, 2025, and conclude by April 13, 2025. Please note that notifications for this maintenance activity will not be viewable on the Amazon Relational Database Service (RDS) Console or through Amazon Command Line Interface (Amazon CLI)/ API.

To minimize disruption to secondary clusters in the future, we recommend upgrading to one of the following Amazon Aurora - PostgreSQL-compatible engine versions: 16.6, 15.10, 14.15, 13.18, 12.22, or higher. These versions include enhancements that prevent read replica restarts during mandatory operating system updates for the Global Database replication infrastructure [1].

You can find a list of your impacted clusters in the 'Affected Resources' tab. Please note that resources created after this notification may not appear in the 'Affected Resources' tab.

If you have any questions or concerns, please reach out to Amazon Web Services China Support [2].

[1] https://docs.amazonaws.cn/en_us/AmazonRDS/latest/AuroraUserGuide/aurora-global-database-secondary-availability.html 
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
我们正在为 Amazon Aurora - PostgreSQL-兼容版 全局数据库复制基础设施的底层软件部署操作系统维护更新。在维护过程中，主集群中的实例不会受到影响，可以继续为应用程序的请求提供服务。但是，更新需要重新启动所有辅助集群实例，这会导致辅助区域中的读副本暂时不可用。重启通常在一分钟内完成。

更新和由此产生的重启将在辅助数据库集群的维护窗口期间进行。维护活动将从2025年3月25日开始，到2025年4月13日结束。请注意，此维护活动的通知无法在亚马逊云科技Amazon Relational Database Service (RDS) 控制台或通过Amazon Command Line Interface (Amazon CLI) / API查看。

为了尽量减少对辅助集群的干扰，我们建议升级到以下 Amazon Aurora - PostgreSQL-兼容版引擎版本之一： 16.6、 15.10、 14.15、 13.18、 12.22 或更高版本。这些版本包含增强功能，可在全局数据库复制基础设施[1]的强制操作系统更新期间防止读副本重启。

您可以在"受影响的资源" 选项卡中找到受影响集群的列表。请注意，在此通知之后创建的资源可能不会出现在 "受影响的资源" 选项卡中。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [2] 。

[1] https://docs.amazonaws.cn/AmazonRDS/latest/AuroraUserGuide/aurora-global-database-secondary-availability.html
[2] https://console.amazonaws.cn/support/
'


样例8:
英文原文:
'
We identified an issue in Patch Manager, a capability of AWS Systems Manager, which affects Patch Baselines for instances running the SUSE Linux Enterprise Server (SLES) operating system. Patch Manager automates the process of patching managed nodes with both security-related updates and non-security Operating System updates. The issue resulted in packages without release dates to have an incorrect patch status of "non-compliant" despite the patches being installed. We identified a SUSE Linux Patch Baseline in your account that is utilizing an auto-approval date configuration. 

On January 8, 2025, we implemented a fix to ensure when a release date is not provided by a package manager on instances running SUSE Linux, Patch Manager will use the build time of the package as the date for auto-approval date configuration in a Patch Baseline. If Patch Manager is unable to find the build time of the package, it will use a default date of January 1, 1970. No action is required by you to address this issue. 

Patch Manager will abide by auto-approval date configurations in a SUSE Patch Baseline for packages where release dates or build times are provided by a package manager. When neither of these dates are provided by a package manager for a specific package, Patch Manager will utilize a default date of January 1, 1970. This will bypass and approve the package update for a SUSE Linux Patch Baseline with auto-approval date configurations enabled for any date after January 1, 1970. For more information about this update and Patch Baseline configurations, please refer to our updated documentation [1].

You can find a list of your affected resources in the 'Affected Resources' tab of your AWS Health Dashboard.

If you have questions or concerns, please contact AWS Support [2].

[1] https://docs.aws.amazon.com/systems-manager/latest/userguide/patch-manager-predefined-and-custom-patch-baselines.html#baselines-auto-approvals
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We identified an issue in Patch Manager, a capability of Amazon Systems Manager, which affects Patch Baselines for instances running the SUSE Linux Enterprise Server (SLES) operating system. Patch Manager automates the process of patching managed nodes with both security-related updates and non-security Operating System updates. The issue resulted in packages without release dates to have an incorrect patch status of "non-compliant" despite the patches being installed. We identified a SUSE Linux Patch Baseline in your account that is utilizing an auto-approval date configuration. 

On January 8, 2025, we implemented a fix to ensure when a release date is not provided by a package manager on instances running SUSE Linux, Patch Manager will use the build time of the package as the date for auto-approval date configuration in a Patch Baseline. If Patch Manager is unable to find the build time of the package, it will use a default date of January 1, 1970. No action is required by you to address this issue. 

Patch Manager will abide by auto-approval date configurations in a SUSE Patch Baseline for packages where release dates or build times are provided by a package manager. When neither of these dates are provided by a package manager for a specific package, Patch Manager will utilize a default date of January 1, 1970. This will bypass and approve the package update for a SUSE Linux Patch Baseline with auto-approval date configurations enabled for any date after January 1, 1970. For more information about this update and Patch Baseline configurations, please refer to our updated documentation [1].

You can find a list of your affected resources in the 'Affected Resources' tab.

If you have any questions or concerns, please contact Amazon Web Services China Support [2].

[1] https://docs.amazonaws.cn/en_us/systems-manager/latest/userguide/patch-manager-predefined-and-custom-patch-baselines.html#baselines-auto-approvals
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
我们发现补丁管理器（Amazon Systems Manager的一个功能）存在一个问题，会影响运行 SUSE Linux Enterprise Server (SLES) 操作系统的实例的补丁基准。补丁管理器自动化了对受管理节点的补丁安装过程，包括与安全相关的更新和非安全的操作系统更新。该问题导致没有发布日期的软件包出现不正确的补丁状态 “不合规”，尽管补丁已被安装。我们在您的账户中识别到一个使用自动批准日期配置的 SUSE Linux 补丁基准。

在2025年1月8日，我们实施了一项修复，以确保当 SUSE Linux 实例上的软件包管理器未提供发布日期时，补丁管理器将使用软件包的构建时间作为补丁基准中自动批准日期配置的日期。如果补丁管理器无法找到软件包的构建时间，它将使用默认日期1970年1月1日。您无需采取任何措施来解决此问题。

补丁管理器将遵循 SUSE 补丁基准中对软件包的自动批准日期配置，前提是软件包管理器提供了发布日期或构建时间。当软件包管理器未提供特定软件包的这两个日期时，补丁管理器将使用默认日期1970年1月1日。这将绕过并批准启用了自动批准日期配置的 SUSE Linux 补丁基准中，任何在1970年1月1日之后的日期的软件包更新。如需有关此更新和补丁基准配置的更多信息，请参阅我们的更新文档 [1]。

您可以在 “受影响的资源” 选项卡中获取您受影响的资源列表。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [2]。

[1] https://docs.amazonaws.cn/systems-manager/latest/userguide/patch-manager-predefined-and-custom-patch-baselines.html#baselines-auto-approvals
[2] https://console.amazonaws.cn/support 
'

样例9:
英文原文:
'
We are reaching out to notify you about an issue that might have impacted you since you are using the named shadow indexing functionality offered under IoT Device Management's fleet indexing service. For an edge case where a Thing was deleted and recreated, and the named shadow was updated within 180 seconds, fleet indexing service missed indexing those named shadow updates during that period. This issue has existed since the named shadow indexing feature launch on November 30, 2021

If you used this feature between November 30, 2021, and March 13, 2025, for targeting jobs using dynamic groups or other use cases, you may want to validate that all intended devices received the jobs or custom actions.

We identified and resolved the root cause on March 13, 2025. We are currently working on restoring the missing named shadow updates that occurred before the fix was deployed. We will send an update once the restoration is complete.

If you have any questions or require further assistance, please contact AWS Support [1].

[1] https://aws.amazon.com/support
'

替换后的英文:
'

We are reaching out to notify you about an issue that might have impacted you since you are using the named shadow indexing functionality offered under Amazon IoT Device Management's fleet indexing service. For an edge case where a Thing was deleted and recreated, and the named shadow was updated within 180 seconds, fleet indexing service missed indexing those named shadow updates during that period. This issue has existed since the named shadow indexing feature launch on November 30, 2021

If you used this feature between November 30, 2021, and March 13, 2025, for targeting jobs using dynamic groups or other use cases, you may want to validate that all intended devices received the jobs or custom actions.

We identified and resolved the root cause on March 13, 2025. We are currently working on restoring the missing named shadow updates that occurred before the fix was deployed. We will send an update once the restoration is complete.

If you have any questions or require further assistance, please contact Amazon Web Services China Support [1].

[1] https://console.amazonaws.cn/support
'

翻译示例:
'
我们联系您是因为您使用了 Amazon IoT Device Management 提供的机群索引服务中的命名影子索引功能，该功能存在一个可能影响到您的问题。在一个特定情况下，如果一个 Thing 被删除并重新创建，并且命名影子在180秒内进行了更新，那么机群索引服务可能会漏掉这些命名影子的更新。这个问题自2021年11月30日命名影子索引功能推出以来一直存在。

如果您在2021年11月30日至2025年3月13日期间使用了这项功能，无论是用于动态分组定位作业还是其他用途，您可能需要确认所有预期的设备是否都正确接收到了作业或自定义操作。

我们已于2025年3月13日确定了问题的根本原因并进行了修复。目前，我们正在努力恢复在修复部署前遗漏的命名影子更新，一旦恢复工作完成，我们将立即向您发送更新通知。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队[1]。

[1] https://console.amazonaws.cn/support
'


样例10:
英文原文:
'
We recently made a change to Amazon Relational Database Service (Amazon RDS) and how AWS Identity and Access Management (IAM) and Service Control Policies (SCPs) are enforced regarding resource tags. With this change, to successfully call Amazon RDS APIs that create, restore, or copy resources where tags are involved, the 'rds:AddTagsToResource' permission is required. Your account has one or more roles or users calling these APIs but which have AWS IAM policies or SCPs that do not have this permission. 

To provide you time to review and take action, we have added your account to an allow list until June 1, 2025. To continue calling these APIs after this date, your IAM policies or SCPs must include the 'rds:AddTagsToResource' permission. After this date, any API calls made without the required 'rds:AddTagsToResource' permission will fail with an "Access Denied" error.

A list of your affected AWS IAM users and roles can be found in the 'Affected resources' tab of your AWS Health Dashboard.

This change applies to the following list of API calls:

CopyDBClusterParameterGroup
CopyDBClusterSnapshot
CopyDBParameterGroup
CopyDBSnapshot
CopyOptionGroup
CreateBlueGreenDeployment
CreateCustomDBEngineVersion
CreateDBCluster
CreateDBClusterEndpoint
CreateDBClusterParameterGroup
CreateDBClusterSnapshot
CreateDBInstance
CreateDBInstanceReadReplica
CreateDBParameterGroup
CreateDBSecurityGroup
CreateDBShardGroup
CreateDBSnapshot
CreateDBSubnetGroup
CreateGlobalCluster
CreateEventSubscription
CreateIntegration
CreateOptionGroup
CreateTenantDatabase
RestoreDBClusterFromS3
RestoreDBClusterFromSnapshot
RestoreDBClusterToPointInTime
RestoreDBInstanceFromDBSnapshot
RestoreDBInstanceFromS3
RestoreDBInstanceToPointInTime

Please refer to the "Actions, resources, and condition keys for Amazon RDS" user guide for a list of the affected APIs which require the 'rds:AddTagsToResource' permission [1].

The following is an example policy that includes the 'rds:AddTagsToResource' action, using one of the above mentioned APIs:

{

    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "CopyDBClusterParameterGroup"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "rds:AddTagsToResource",
            "Resource": "*"
        }
    ]
}

If you have questions or concerns, please contact AWS Support [2].

[1] https://docs.aws.amazon.com/service-authorization/latest/reference/list_amazonrds.html
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We recently made a change to Amazon Relational Database Service (Amazon RDS) and how Amazon Identity and Access Management (IAM) and Service Control Policies (SCPs) are enforced regarding resource tags. With this change, to successfully call Amazon RDS APIs that create, restore, or copy resources where tags are involved, the 'rds:AddTagsToResource' permission is required. Your account has one or more roles or users calling these APIs but which have Amazon IAM policies or SCPs that do not have this permission. 

To provide you time to review and take action, we have added your account to an allow list until June 1, 2025. To continue calling these APIs after this date, your IAM policies or SCPs must include the 'rds:AddTagsToResource' permission. After this date, any API calls made without the required 'rds:AddTagsToResource' permission will fail with an "Access Denied" error.

A list of your affected Amazon IAM users and roles can be found in the 'Affected resources' tab.

This change applies to the following list of API calls:

CopyDBClusterParameterGroup
CopyDBClusterSnapshot
CopyDBParameterGroup
CopyDBSnapshot
CopyOptionGroup
CreateBlueGreenDeployment
CreateCustomDBEngineVersion
CreateDBCluster
CreateDBClusterEndpoint
CreateDBClusterParameterGroup
CreateDBClusterSnapshot
CreateDBInstance
CreateDBInstanceReadReplica
CreateDBParameterGroup
CreateDBSecurityGroup
CreateDBShardGroup
CreateDBSnapshot
CreateDBSubnetGroup
CreateGlobalCluster
CreateEventSubscription
CreateIntegration
CreateOptionGroup
CreateTenantDatabase
RestoreDBClusterFromS3
RestoreDBClusterFromSnapshot
RestoreDBClusterToPointInTime
RestoreDBInstanceFromDBSnapshot
RestoreDBInstanceFromS3
RestoreDBInstanceToPointInTime

Please refer to the "Actions, resources, and condition keys for Amazon RDS" user guide for a list of the affected APIs which require the 'rds:AddTagsToResource' permission [1].

The following is an example policy that includes the 'rds:AddTagsToResource' action, using one of the above mentioned APIs:

{

    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "CopyDBClusterParameterGroup"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "rds:AddTagsToResource",
            "Resource": "*"
        }
    ]
}

If you have any questions or concerns, please contact Amazon Web Services China Support [2].

[1] https://docs.amazonaws.cn/en_us/service-authorization/latest/reference/list_amazonrds.html
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
我们最近更改了 Amazon Relational Database Service (Amazon RDS)，以及如何针对资源标签执行 Amazon Identity and Access Management (IAM) 和 Service Control Policies (SCPs)。有了这个更改，要成功调用 Amazon RDS API 来创建、恢复或复制涉及标签的资源，就需要 'rds:AddTagsToResource' 权限。 您的账户有一个或多个调用这些 API 的角色或用户，但这些角色或用户的 Amazon IAM 策略或 SCPs 没有此权限。

为了让您有时间审查并采取行动，我们已将您的账户添加到允许列表中，持续到 2025 年 6 月 1 日。 要在此日期之后继续调用这些 API，您的 Amazon IAM 策略或 SCP 必须包含 'rds:AddTagsToResource' 权限。 在此日期之后，任何没有所需的 'rds:AddTagsToResource' 权限的 API 调用都将失败，并出现 "Access Denied" 错误。

您可以在 "受影响的资源" 选项卡中查看受影响的 Amazon IAM 用户和角色列表。

此更改适用于以下API调用列表：

CopyDBClusterParameterGroup
CopyDBClusterSnapshot
CopyDBParameterGroup
CopyDBSnapshot
CopyOptionGroup
CreateBlueGreenDeployment
CreateCustomDBEngineVersion
CreateDBCluster
CreateDBClusterEndpoint
CreateDBClusterParameterGroup
CreateDBClusterSnapshot
CreateDBInstance
CreateDBInstanceReadReplica
CreateDBParameterGroup
CreateDBSecurityGroup
CreateDBShardGroup
CreateDBSnapshot
CreateDBSubnetGroup
CreateGlobalCluster
CreateEventSubscription
CreateIntegration
CreateOptionGroup
CreateTenantDatabase
RestoreDBClusterFromS3
RestoreDBClusterFromSnapshot
RestoreDBClusterToPointInTime
RestoreDBInstanceFromDBSnapshot
RestoreDBInstanceFromS3
RestoreDBInstanceToPointInTime

有关需要 'rds:AddTagsToResource' 权限 [1] 的受影响 API 列表，请参阅 "Amazon RDS 的操作、资源和条件键" 用户指南。

下面是一个包含 'rds:AddTagsToResource' 操作的策略示例，使用了其中一个上面提到的 API：

{

    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "CopyDBClusterParameterGroup"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "rds:AddTagsToResource",
            "Resource": "*"
        }
    ]
}

如果您有任何问题或者疑虑，请联系亚马逊云科技中国支持团队 [2]。

[1] https://docs.amazonaws.cn/service-authorization/latest/reference/list_amazonrds.html
[2] https://console.amazonaws.cn/support 
'


样例11:
英文原文:
'
Service: WORKSPACES
Region:  BJS | ZHY   
Failure mode 1: 
TypeCode:  AWS_WORKSPACES_SIGNIN_ISSUE
Wording: We are investigating an increase in errors for the AWS WorkSpaces sign-in in the {{region}} Region.
'

替换后的英文:
'
Service: WORKSPACES
Region: ZHY
Failure mode 1:
TypeCode: AWS_WORKSPACES_SIGNIN_ISSUE
Wording: We are investigating an increase in errors for the Amazon Workspaces sign-in in the Ningxia Region.
'

翻译示例:
'
Service: WORKSPACES
Region: ZHY
Failure mode 1:
TypeCode: AWS_WORKSPACES_SIGNIN_ISSUE
Wording: 我们正在调查宁夏区域Amazon Workspaces登录时错误率增加的问题。
'


样例12:
英文原文:
'
We identified an issue with AWS Database Migration Service (AWS DMS), which may require your action. The issue resulted in AWS DMS replication logs not streaming to AWS CloudWatch when the dms-cloudwatch-logs-role AWS Identity and Access Management (AWS IAM) role is configured with aws:SourceAccount [1] and aws:SourceArn [2] conditions. We have addressed this issue and can confirm DMS is operating as expected. However, we identified you removed aws:SourceAccount and aws:SourceArn global condition context keys from your dms-cloudwatch-logs-role IAM role trust policy. We recommend configuring your dms-cloudwatch-logs-role IAM role in accordance with DMS cross-service confused deputy prevention guidance [3] to protect against potential cross-service confused deputy issues.

Please refer to the "Affected Resources" tab of your AWS Health Dashboard for affected dms-cloudwatch-logs-role IAM role.

If you have any questions or concerns, please reach out to AWS Support [4].

[1] https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourceaccount
[2] https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourcearn
[3] https://docs.aws.amazon.com/dms/latest/userguide/cross-service-confused-deputy-prevention.html
[4] https://aws.amazon.com/support
'

替换后的英文:
'
Hello,

We identified an issue with Amazon Database Migration Service (Amazon DMS), which may require your action. The issue resulted in Amazon DMS replication logs not streaming to Amazon CloudWatch when the dms-cloudwatch-logs-role Amazon Identity and Access Management (Amazon IAM) role is configured with aws:SourceAccount [1] and aws:SourceArn [2] conditions. We have addressed this issue and can confirm Amazon DMS is operating as expected. However, we identified you removed aws:SourceAccount and aws:SourceArn global condition context keys from your dms-cloudwatch-logs-role IAM role trust policy. We recommend configuring your dms-cloudwatch-logs-role IAM role in accordance with Amazon DMS cross-service confused deputy prevention guidance [3] to protect against potential cross-service confused deputy issues.

Please refer to the "Affected Resources" tab of your Amazon Health Dashboard for affected dms-cloudwatch-logs-role IAM role.

If you have any questions or concerns, please contact Amazon Web Services China Support [4].

[1] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourceaccount
[2] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourcearn
[3] https://docs.amazonaws.cn/en_us/dms/latest/userguide/cross-service-confused-deputy-prevention.html
[4] https://console.amazonaws.cn/support/
'

翻译示例:
'
您好，

我们发现了 Amazon Database Migration Service (Amazon DMS)存在的一个问题，可能需要您采取行动。 当 dms-cloudwatch-logs-role Amazon Identity and Access Management (IAM) 角色配置了 aws:SourceAccount [1] 和 aws:SourceArn [2] 条件时， Amazon DMS 复制日志无法被发送到 Amazon CloudWatch。 我们已经解决了这个问题，可以确认 Amazon DMS 按预期运行。 但是，我们发现您从您的 dms-cloudwatch-logs-role IAM 角色信任策略中删除了 aws:SourceAccount 和 aws: SourceArn 全局条件上下文关键字。 我们建议您根据 Amazon DMS 跨服务混淆代理预防指南 [3] 配置您的 dms-cloudwatch-logs-role IAM 角色，以防止潜在的跨服务混淆代理问题。

您可以在 Amazon Health Dashboard 的“受影响的资源”选项卡中查看受影响的 dms-cloudwatch-logs-role IAM 角色。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [4] 。

[1] https://docs.amazonaws.cn/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourceaccount
[2] https://docs.amazonaws.cn/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-sourcearn
[3] https://docs.amazonaws.cn/dms/latest/userguide/cross-service-confused-deputy-prevention.html
[4] https://console.amazonaws.cn/support/
'


样例13:
英文原文:
'
We identified that your Amazon OpenSearch Service domain has been in an inaccessible state due to missing VPC subnet security groups for over 90 days. As a result, your domain will be isolated on March 22, 2025. We have contacted you on multiple occasions to address this issue in your domain, however we have yet to receive a response from you. To resume complete operations on your domain, configure at least one security group by March 21, 2025. 

If you do not take action by March 21, 2025, we will remove the network access to your domain and mark its status as isolated on March 22, 2025. Once isolated, your domain along with its data will be deleted on March 31, 2025. 

Please refer to the "Affected resources" tab of your AWS Health Dashboard for a list of your affected OpenSearch Service domains.

For information on how to configure security groups, please refer to the documentation [1]. 

If you want assistance with enabling security groups for your VPC domain, please contact your account manager or contact AWS Support [2]. 

[1] https://docs.aws.amazon.com/opensearch-service/latest/developerguide/vpc.html#vpc-security
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We identified that your Amazon OpenSearch Service (successor to Amazon Elasticsearch Service) domain has been in an inaccessible state due to missing Amazon Virtual Private Cloud (VPC) subnet security groups for over 90 days. As a result, your domain will be isolated on March 22, 2025. We have contacted you on multiple occasions to address this issue in your domain, however we have yet to receive a response from you. To resume complete operations on your domain, configure at least one security group by March 21, 2025. 

If you do not take action by March 21, 2025, we will remove the network access to your domain and mark its status as isolated on March 22, 2025. Once isolated, your domain along with its data will be deleted on March 31, 2025. 

Please refer to the "Affected resources" tab for a list of your affected Amazon OpenSearch Service domains.

For information on how to configure security groups, please refer to the documentation [1]. 

If you want assistance with enabling security groups for your VPC domain, please contact Amazon Web Services China Support [2]. 

[1] https://docs.amazonaws.cn/en_us/opensearch-service/latest/developerguide/vpc.html#vpc-security
[2] https://console.amazonaws.cn/support/
'

翻译示例:
'
我们识别到您的 Amazon OpenSearch Service (successor to Amazon Elasticsearch Service) 域由于缺少 Amazon Virtual Private Cloud (VPC) 子网安全组而处于不可访问状态超过90天。因此，您的域将在2025年3月22日被隔离。我们已经多次与您联系以解决您的域中的此问题，但我们尚未收到您的回复。要恢复域上的完整操作，请在2025年3月21日之前配置至少一个安全组。

如果您在2025年3月21日之前不采取行动，我们将删除对您域的网络访问，并在2025年3月22日将其标记为隔离状态。一旦被隔离，您的域及其数据将在2025年3月31日被删除。

您可以在 “ 受影响的资源 ”栏中查看受影响的 Amazon OpenSearch 服务域的列表。

有关如何配置安全组的信息，请参阅文档[1]。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [2]。

[1] https://docs.amazonaws.cn/opensearch-service/latest/developerguide/vpc.html#vpc-security
[2] https://console.amazonaws.cn/support/
'


样例14:
英文原文:
'
We are contacting you to inform you of an upcoming change to the CreateVolume API of Amazon Elastic Block Store (Amazon EBS). You may have received a notification about this change. Our records now indicate that you will be impacted by the change. We received a large number of requests from customers following the first notification who need additional time to prepare for the upcoming policy updates. After careful consideration, we have decided to extend the launch timeline. Beginning February 17, 2025, we will add authorization for the source snapshots in CreateVolume requests. Additionally, in the source snapshots, we will be also launching support for Resource Owner global condition keys [1] and seven EC2-specific keys: ec2:ProductCode, ec2:Encrypted, ec2:VolumeSize, ec2:ParentSnapshot, ec2:Owner, ec2:ParentVolume and ec2:SnapshotTime. Currently, these condition keys are not supported. [2] To avoid impact, you will need to update your IAM policies.

What will be changed through this update?
Previously, the CreateVolume action only required authorization for the volume getting created, but not for the source snapshot from which the volume were created. However, after February 17, 2025, we will be also authorizing the source snapshot. We identified your account has made calls to the CreateVolume API with a permissions policy that will be impacted by this change. Currently, your requests to invoke CreateVolume API may be allowed, but after February 17, 2025 they may be denied once AWS begins authorizing the source snapshot.

Which IAM policies should I update?
We recommend you review your IAM policies that contain the CreateVolume action. In your current policy, you may have specified a principal or multiple principals, with or without condition keys to create a volume from a source snapshot. Ensure your policies are set up to allow or deny access to the source snapshot and volume getting created, or create separate IAM policy statements for each. Policies requiring updates generally fall into the below two categories:

Category 1: There is no snapshot Resource element

If your policy's "Resource" element only matched volumes, you need to add a new statement to authorize snapshots. see below:

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws:ec2:*::snapshot/*"
}

Category 2: A Resource element incorrectly applies to both volumes and snapshots

If your policy's "Resource" element used a wildcard to match both volumes and snapshots and included "Condition" elements, you'll need to modify the Resource string to apply only to volumes and add a new statement to authorize snapshots. See below:

...
"Action": "ec2:CreateVolume",
"Resource": "*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

Would become

...
"Action": "ec2:CreateVolume",
"Resource": "arn:aws:ec2:*::volume/*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

And then add a new clause to authorize access to the source snapshot:

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws:ec2:*::snapshot/*"
}

How do you validate if your IAM policies are correct?
If you need to validate your IAM policies are correct, use Dry Run mode to test that your changes to IAM policies are correct. When a policy needs to be updated, the Dry Run call will fail but actual API calls will continue to succeed until we enable the changes to the API for your account. Refer to the CLI documentation [3] to find out more about the Dry Run option. To determine how your IAM policies need to be updated:

* Determine the parameters used by the API would fail permissions checks by making use of CloudTrail logs to identify which parameters are being used.
* Make a Dry Run request using the identified parameters.
* If the Dry Run request returns “An error occurred (DryRunOperation) when calling the API operation: Request would have succeeded, but Dry Run flag is set”, your policy for this API action has already been updated or the resources used in the call have changed.
* If the Dry Run request returns “An error occurred (UnauthorizedOperation) when calling the API operation: You are not authorized to perform this operation. Encoded authorization failure message: Encoded String", there is an issue with your IAM policy for this API and you will need to determine which resource and/or its tags are the reason for failure and update your IAM policy accordingly.

If you need assistance with updating your IAM policies, please review our CLI User Guide [3] or reach out to the AWS Support [4].

[1] https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-resource-properties
[2] https://docs.aws.amazon.com/service-authorization/latest/reference/list_amazonec2.html#amazonec2-resources-for-iam-policies:~:text=ec2%3ARegion-,CopySnapshot,-Grants%20permission%20to
[3] https://docs.aws.amazon.com/cli/latest/userguide/cli-usage-help.html
[4] https://aws.amazon.com/contact-us/
'

替换后的英文:
'
We are contacting you to inform you of an upcoming change to the CreateVolume API of Amazon Elastic Block Store (EBS). You may have received a notification about this change. Our records now indicate that you will be impacted by the change. We received a large number of requests from customers following the first notification who need additional time to prepare for the upcoming policy updates. After careful consideration, we have decided to extend the launch timeline. Beginning February 17, 2025, we will add authorization for the source snapshots in CreateVolume requests. Additionally, in the source snapshots, we will be also launching support for Resource Owner global condition keys [1] and seven Amazon Elastic Compute Cloud (EC2)-specific keys: ec2:ProductCode, ec2:Encrypted, ec2:VolumeSize, ec2:ParentSnapshot, ec2:Owner, ec2:ParentVolume and ec2:SnapshotTime. Currently, these condition keys are not supported. [2] To avoid impact, you will need to update your Amazon Identity and Access Management (IAM) policies.

What will be changed through this update?
Previously, the CreateVolume action only required authorization for the volume getting created, but not for the source snapshot from which the volume were created. However, after February 17, 2025, we will be also authorizing the source snapshot. We identified your account has made calls to the CreateVolume API with a permissions policy that will be impacted by this change. Currently, your requests to invoke CreateVolume API may be allowed, but after February 17, 2025 they may be denied once Amazon Web Services begins authorizing the source snapshot.

Which IAM policies should I update?
We recommend you review your IAM policies that contain the CreateVolume action. In your current policy, you may have specified a principal or multiple principals, with or without condition keys to create a volume from a source snapshot. Ensure your policies are set up to allow or deny access to the source snapshot and volume getting created, or create separate IAM policy statements for each. Policies requiring updates generally fall into the below two categories:

Category 1: There is no snapshot Resource element

If your policy's "Resource" element only matched volumes, you need to add a new statement to authorize snapshots. see below:

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::snapshot/*"
}

Category 2: A Resource element incorrectly applies to both volumes and snapshots

If your policy's "Resource" element used a wildcard to match both volumes and snapshots and included "Condition" elements, you'll need to modify the Resource string to apply only to volumes and add a new statement to authorize snapshots. See below:

...
"Action": "ec2:CreateVolume",
"Resource": "*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

Would become

...
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::volume/*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

And then add a new clause to authorize access to the source snapshot:

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::snapshot/*"
}

How do you validate if your IAM policies are correct?
If you need to validate your IAM policies are correct, use Dry Run mode to test that your changes to IAM policies are correct. When a policy needs to be updated, the Dry Run call will fail but actual API calls will continue to succeed until we enable the changes to the API for your account. Refer to the Amazon Command Line Interface (Amazon CLI) documentation [3] to find out more about the Dry Run option. To determine how your IAM policies need to be updated:

* Determine the parameters used by the API would fail permissions checks by making use of Amazon CloudTrail logs to identify which parameters are being used.
* Make a Dry Run request using the identified parameters.
* If the Dry Run request returns “An error occurred (DryRunOperation) when calling the API operation: Request would have succeeded, but Dry Run flag is set”, your policy for this API action has already been updated or the resources used in the call have changed.
* If the Dry Run request returns “An error occurred (UnauthorizedOperation) when calling the API operation: You are not authorized to perform this operation. Encoded authorization failure message: Encoded String", there is an issue with your IAM policy for this API and you will need to determine which resource and/or its tags are the reason for failure and update your IAM policy accordingly.

If you need assistance with updating your IAM policies, please review our Amazon CLI User Guide [3] or contact Amazon Web Services China Support [4].

[1] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-resource-properties
[2] https://docs.amazonaws.cn/en_us/service-authorization/latest/reference/list_amazonec2.html#amazonec2-resources-for-iam-policies:~:text=ec2%3ARegion-,CopySnapshot,-Grants%20permission%20to
[3] https://docs.amazonaws.cn/en_us/cli/latest/userguide/cli-usage-help.html
[4] https://console.amazonaws.cn/support/
'

翻译示例:
'
我们联系您是为了通知您，Amazon Elastic Block Store (EBS) 的 CreateVolume API 即将发生变更。您可能已经收到过有关此变更的通知。我们的记录显示您将受到该变更的影响。在第一次通知后，我们收到了大量客户的反馈，他们需要更多时间为即将到来的策略更新做准备。经过慎重考虑，我们决定延长推出时间。从 2025 年 2 月 17 日起，我们将在 CreateVolume 请求中添加对源快照的授权。此外，在源快照中，我们还将推出对资源所有者全局条件键 [1] 和七个 Amazon Elastic Compute Cloud (EC2) 特定条件键的支持：ec2:ProductCode、ec2:Encrypted、ec2:VolumeSize、ec2:ParentSnapshot、ec2:Owner、ec2:ParentVolume 和 ec2:SnapshotTime。目前，不支持这些条件键 [2]。为了避免受到影响，您需要更新您的 Amazon Identity and Access Management (IAM) 策略。

此次更新将带来哪些变化？
以前，CreateVolume 操作只需要对正在创建的卷进行授权，而不要求对创建卷的源快照进行授权。但在 2025 年 2 月 17 日之后，我们还将对源快照进行授权。我们识别到您的账户已调用 CreateVolume API，其权限策略将受到此次变更的影响。 目前，您调用 CreateVolume API 的请求可能会被允许，但是在 2025 年 2 月 17 日之后，亚马逊云科技开始授权源快照后，这些请求可能会被拒绝。

我应该更新哪些 IAM 策略？
我们建议您查看包含 CreateVolume 操作的 IAM 策略。在您当前的策略中，您可能已经指定了一个或多个主体，可以使用或者不使用条件键从源快照创建卷。确保您的策略设置为允许或拒绝访问正在创建的源快照和卷，或者为每个快照和卷创建单独的 IAM 策略声明。需要更新的策略通常分为以下两类：

类别 1：没有快照资源元素

如果您的策略的 “资源” 元素仅匹配卷，则需要添加新的语句来授权快照。参见下文：

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::snapshot/*"
}

类别 2：资源元素错误地同时适用于卷和快照

如果您的策略的 “资源” 元素使用通配符来匹配卷和快照，并且包含 “条件” 元素，则需要修改资源字符串以仅应用于卷，并添加新的语句来授权快照。 见下文：

...
"Action": "ec2:CreateVolume",
"Resource": "*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

会变成

...
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::volume/*",
"Condition": { "StringLike": { "aws:RequestTag/CSIVolumeName": "*" } }
...

然后添加一个新语句来授权访问源快照：

{
"Effect": "Allow",
"Action": "ec2:CreateVolume",
"Resource": "arn:aws-cn:ec2:*::snapshot/*"
}

如何验证您的 IAM 策略是否正确？
如果您需要验证您的 IAM 策略是否正确，请使用 Dry Run 模式来测试您对 IAM 策略的更改是否正确。当需要更新策略时，Dry Run 调用将失败，但实际的 API 调用将继续成功，直到我们为您的账户启用 API 更改。 有关 Dry Run 选项的更多信息，请参阅 Amazon Command Line Interface (Amazon CLI) 文档 [3]。要确定您的 IAM 策略需要如何更新，请执行以下操作：

* 使用 Amazon CloudTrail 日志确定正在使用哪些参数，从而确定 API 使用的参数是否无法通过权限检查。
* 使用识别的参数发送 Dry Run 请求。
* 如果 Dry Run 请求返回 “调用 API 操作时发生错误 (dryrunOperation)：请求本来可以成功，但设置了 Dry Run 标志”，则说明您针对此 API 操作的策略已更新或调用中使用的资源已更改。
* 如果 Dry Run 请求返回 “调用 API 操作时出现错误（unauthorizedOperation）：您无权执行此操作。编码授权失败消息：Encoded String”，您针对此 API 的 IAM 策略存在问题，您需要确定哪些资源和/或其标签是失败的原因，并相应地更新您的 IAM 策略。

如果您在更新 IAM 策略方面需要帮助，请查看我们的 Amazon CLI 用户指南 [3] 或联系亚马逊云科技中国支持团队 [4]。

[1] https://docs.amazonaws.cn/IAM/latest/UserGuide/reference_policies_condition-keys.html#condition-keys-resource-properties
[2] https://docs.amazonaws.cn/service-authorization/latest/reference/list_amazonec2.html#amazonec2-resources-for-iam-policies:~:text=ec2%3ARegion-,CopySnapshot,-Grants%20permission%20to
[3] https://docs.amazonaws.cn/cli/latest/userguide/cli-usage-help.html
[4] https://console.amazonaws.cn/support/
'


样例15:
英文原文:
'
In June 2024, we communicated to you about a change to AWS Systems Manager Session Manager where users with an AWS Identity and Access Management (IAM) policy scoped down to allow specific session documents must explicitly grant access to the SSM-SessionManagerRunShell document to start interactive shell sessions. We identified you have not updated your policies to explicitly grant access to the SSM-SessionManagerRunShell document to start interactive shell sessions. A list of your affected resource(s) can be found in the 'Affected resources' tab of your AWS Health Dashboard.

We had communicated that if you did not take action before September 17, 2024, you would be removed from the allow list and would be unable to access interactive shell sessions. However, we have observed that you have not updated your policies and would be affected if we remove you from the allow list as planned. To avoid disruptions to accessing your instances, we are extending the use of the allow list until December 7, 2024. We strongly recommend you update your policy by this date to avoid any impact.

The following is an example policy that no longer allows interactive shell sessions once your account is removed from the allow list:

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws:ec2:us-west-2:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws:ssm:us-west-2:************:document/SSM-StartPortForwardingSession"
      ],
    }
  ]
}

The following is an example of a updated policy that will continue to have access to interactive shell sessions in addition to existing allowed policies:

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws:ec2:us-west-2:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws:ssm:us-west-2:************:document/SSM-StartPortForwardingSession"
        "arn:aws:ssm:us-west-2:************:document/SSM-SessionManagerRunShell"
      ],
    }
  ]
}

Please refer to the "Creating IAM Policies" [1] and "AWS Systems Manager Session Manager" [2] user guides for additional information. For additional sample policies, please refer to the "Sample IAM policies for Session Manager" [3] and "Additional sample IAM policies for Session Manager" [4] user guides.

If you have questions or concerns, please contact AWS Support [5].

[1] https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies_create.html
[2] https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager.html
[3] https://docs.aws.amazon.com/systems-manager/latest/userguide/getting-started-restrict-access-quickstart.html
[4] https://docs.aws.amazon.com/systems-manager/latest/userguide/getting-started-restrict-access-examples.html
[5] https://aws.amazon.com/support
'

替换后的英文:
'
In June 2024, we communicated to you about a change to Amazon Systems Manager Session Manager where users with an Amazon Identity and Access Management (IAM) policy scoped down to allow specific session documents must explicitly grant access to the SSM-SessionManagerRunShell document to start interactive shell sessions. We identified you have not updated your policies to explicitly grant access to the SSM-SessionManagerRunShell document to start interactive shell sessions. A list of your affected resource(s) can be found in the 'Affected resources' tab.

We had communicated that if you did not take action before September 17, 2024, you would be removed from the allow list and would be unable to access interactive shell sessions. However, we have observed that you have not updated your policies and would be affected if we remove you from the allow list as planned. To avoid disruptions to accessing your instances, we are extending the use of the allow list until December 7, 2024. We strongly recommend you update your policy by this date to avoid any impact.

The following is an example policy that no longer allows interactive shell sessions once your account is removed from the allow list:

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws-cn:ec2:cn-northwest-1:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-StartPortForwardingSession"
      ],
    }
  ]
}

The following is an example of a updated policy that will continue to have access to interactive shell sessions in addition to existing allowed policies:

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws-cn:ec2:cn-northwest-1:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-StartPortForwardingSession"
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-SessionManagerRunShel"
      ],
    }
  ]
}

Please refer to the "Creating IAM Policies" [1] and "Amazon Systems Manager Session Manager" user guides for additional information [2]. For additional sample policies, please refer to the "Sample IAM policies for Session Manager" [3] and "Additional sample IAM policies for Session Manager" user guides [4].

If you have any questions or concerns, please contact Amazon Web Services China Support [5].

[1] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/access_policies_create.html
[2] https://docs.amazonaws.cn/en_us/systems-manager/latest/userguide/session-manager.html
[3] https://docs.amazonaws.cn/en_us/systems-manager/latest/userguide/getting-started-restrict-access-quickstart.html
[4] https://docs.amazonaws.cn/en_us/systems-manager/latest/userguide/getting-started-restrict-access-examples.html
[5] https://console.amazonaws.cn/support
'

翻译示例:
'
在2024 年 6 月，我们向您通报了 Amazon Systems Manager 会话管理器的一项变更。在该变更中，拥有 Amazon Identity and Access Management (IAM)策略范围 缩小为 以允许特定会话文档的用户必须明确授予对 SSM-SessionManagerRunShell 文档的访问权限，才能启动交互式 shell 会话。我们发现您尚未更新策略，以明确授予对 SSM-SessionManagerRunShell 文档的访问权限，从而启动交互式 shell 会话。您可以在 “受影响资源” 栏中找到可能受影响资源的列表。

我们此前已经告知您，如果您在 2024 年 9 月 17 日之前没有采取任何行动，您将被从允许列表中删除，并且将无法访问交互式 shell 会话。但是，我们发现您尚未更新策略，如果我们按计划将您从允许列表中删除，您将会受到影响。 为了避免您的实例访问中断，我们将允许列表的使用期限延长至 2024 年 12 月 7 日。 我们强烈建议您在此日期之前更新您的策略，以免产生影响。

下面是一个策略示例，一旦账户从允许列表中删除，就不再允许使用交互式 shell 会话：

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws-cn:ec2:cn-northwest-1:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-StartPortForwardingSession"
      ],
    }
  ]
}

以下是更新后的策略示例，除了现有的允许策略外，添加该策略将继续允许访问交互式 shell 会话：

{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "EnableSSMSession",
      "Effect": "Allow",
      "Action": [
        "ssm:StartSession"
      ],
      "Resource": [
        "arn:aws-cn:ec2:cn-northwest-1:************:instance/i-02573cafcfEXAMPLE",
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-StartPortForwardingSession"
        "arn:aws-cn:ssm:cn-northwest-1:************:document/SSM-SessionManagerRunShel"
      ],
    }
  ]
}

有关更多信息，请参考 “创建 IAM 策略” [1] 和 “Amazon Systems Manager Session Manager” 用户指南 [2]。 有关其他示例策略，请参考 “Session Manager 的 IAM 策略示例” [3] 和 “Session Manager 的其他 IAM 策略示例” 的用户指南 [4]。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [5]。

[1] https://docs.amazonaws.cn/IAM/latest/UserGuide/access_policies_create.html
[2] https://docs.amazonaws.cn/systems-manager/latest/userguide/session-manager.html
[3] https://docs.amazonaws.cn/systems-manager/latest/userguide/getting-started-restrict-access-quickstart.html
[4] https://docs.amazonaws.cn/systems-manager/latest/userguide/getting-started-restrict-access-examples.html
[5] https://console.amazonaws.cn/support/
'


样例16:
英文原文:
'
On October 2, 2024, you received a notification regarding "Amazon Elastic Beanstalk Tomcat 8.5 with Corretto 11 on AL2 platform branches to retire". This notification was sent in error with incorrect example commands. The example should have been:
aws elasticbeanstalk describe-environments --region cn-northwest-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text
Please disregard this notification. For reference, the original notification that was sent in error is provided as follows:

================
Original Message
================

Amazon Elastic Beanstalk is informing you that platform branches [1] based on Tomcat 8.5 with Corretto 11 on Amazon Linux 2 will retire on September 30, 2024. This is to ensure that our customer's Amazon Elastic Beanstalk environments are aligned with the most current support offered by Amazon Web Services.

* What does retire mean, and what will happen after September 30, 2024?

Starting on September 30, 2024, retired platform branches will no longer be available for new environments on Elastic Beanstalk. Customers can continue to operate existing environments running on retired platform branches, but these branches will no longer be managed and supported by Elastic Beanstalk. For more information, see Elastic Beanstalk platform support policy [2] in the Amazon Elastic Beanstalk Developer Guide.

* What do I need to do now?

You have until September 30, 2024, to upgrade your Elastic Beanstalk environments configured on platform branches running on Tomcat 8.5 with Corretto 11 on Amazon Linux 2. We strongly recommend that you start planning your migration to one of the Tomcat platforms, which are current and fully supported:

- Corretto 8 with Tomcat 9 AL2 
- Corretto 11 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 10 AL2023

* Which of my Amazon Elastic Beanstalk environments are impacted?

A list of your affected Beanstalk environments is available in the 'Affected resources' tab of your Amazon Health Dashboard. 

You can also use the Amazon Command Line Interface (Amazon CLI) "describe-environments" command to display a filtered list of all environments that are configured with Tomcat 8.5 with Corretto 11 on Amazon Linux 2. See the following example command:

aws elasticbeanstalk describe-environments --region cn-north-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text

For more information see the Amazon CLI Command Reference for Elastic Beanstalk [3].

* Where can I find more information about Elastic Beanstalk platform support?

Retiring platform branch versions are listed in the Retiring platform versions [4] page of the Amazon Elastic Beanstalk Platforms Guide (They are no longer listed on the Supported platforms [5] page). For more information, see Elastic Beanstalk platform support policy [2] in the Amazon Elastic Beanstalk Developer Guide. 

If you have any questions or concerns, please contact Amazon Web Services China Support [6]. 

[1] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/dg/platforms-glossary.html
[2] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/dg/platforms-support-policy.html
[3] https://docs.amazonaws.cn/cli/latest/reference/elasticbeanstalk/describe-environments.html
[4] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/platforms/platforms-retiring.html
[5] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/platforms/platforms-supported.html
[6] https://console.amazonaws.cn/support/
'

替换后的英文:
'
On October 2, 2024, you received a notification regarding " Amazon Elastic Beanstalk Tomcat 8.5 with Corretto 11 on AL2 platform branches to retire ". This notification was sent in error with incorrect example commands. The example should have been:
aws elasticbeanstalk describe-environments --region cn-northwest-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text
Please disregard this notification. For reference, the original notification that was sent in error is provided as follows:

================
Original Message
================

Amazon Elastic Beanstalk is informing you that platform branches [1] based on Tomcat 8.5 with Corretto 11 on Amazon Linux 2 will retire on September 30, 2024. This is to ensure that our customer's Amazon Elastic Beanstalk environments are aligned with the most current support offered by Amazon Web Services.

* What does retire mean, and what will happen after September 30, 2024?

Starting on September 30, 2024, retired platform branches will no longer be available for new environments on Elastic Beanstalk. Customers can continue to operate existing environments running on retired platform branches, but these branches will no longer be managed and supported by Elastic Beanstalk. For more information, see Elastic Beanstalk platform support policy [2] in the Amazon Elastic Beanstalk Developer Guide.

* What do I need to do now?

You have until September 30, 2024, to upgrade your Elastic Beanstalk environments configured on platform branches running on Tomcat 8.5 with Corretto 11 on Amazon Linux 2. We strongly recommend that you start planning your migration to one of the Tomcat platforms, which are current and fully supported:

- Corretto 8 with Tomcat 9 AL2 
- Corretto 11 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 10 AL2023

* Which of my Amazon Elastic Beanstalk environments are impacted?

A list of your affected Beanstalk environments is available in the 'Affected resources' tab. 

You can also use the Amazon Command Line Interface (Amazon CLI) "describe-environments" command to display a filtered list of all environments that are configured with Tomcat 8.5 with Corretto 11 on Amazon Linux 2. See the following example command:

aws elasticbeanstalk describe-environments --region cn-north-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text

For more information see the Amazon CLI Command Reference for Elastic Beanstalk [3].

* Where can I find more information about Elastic Beanstalk platform support?

Retiring platform branch versions are listed in the Retiring platform versions [4] page of the Amazon Elastic Beanstalk Platforms Guide (They are no longer listed on the Supported platforms [5] page). For more information, see Elastic Beanstalk platform support policy [2] in the Amazon Elastic Beanstalk Developer Guide. 

If you have any questions or concerns, please contact Amazon Web Services China Support [6]. 

[1] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/dg/platforms-glossary.html
[2] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/dg/platforms-support-policy.html
[3] https://docs.amazonaws.cn/cli/latest/reference/elasticbeanstalk/describe-environments.html
[4] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/platforms/platforms-retiring.html
[5] https://docs.amazonaws.cn/en_us/elasticbeanstalk/latest/platforms/platforms-supported.html
[6] https://console.amazonaws.cn/support/
'

翻译示例:
'
2024 年 10 月 2 日，您收到了有关 "Amazon Elastic Beanstalk 中基于 AL2 的 Tomcat 8.5 with Corretto 11 版本的平台分支将停用"的通知。 该通知发送有误，包含错误的示例命令。 正确示例命令应为： 
aws elasticbeanstalk describe-environments --region cn-northwest-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text
请忽略此通知。 错误发送的原始通知如下，以供参考：
================
原始通知
================
我们通知您，基于 Amazon Linux 2 上的 Tomcat 8.5 with Corretto 11 版本的平台分支 [1] 将于 2024 年 9 月 30 日停用。这是为了确保我们客户的 Amazon Elastic Beanstalk 环境与亚马逊云科技提供的最新支持保持一致。

* 停用意味着什么，以及2024 年 9 月 30 日之后会发生什么？

从 2024 年 9 月 30 日开始，停用的平台分支将不再可用于 Elastic Beanstalk 上的新环境。客户可以继续操作在已停用的平台分支上运行的现有环境，但这些分支将不再受 Elastic Beanstalk 的管理和支持。更多信息，请参阅 Amazon Elastic Beanstalk 开发人员指南 中的 Elastic Beanstalk 平台支持策略 [2]。

* 我现在需要做什么？

您需要在 2024 年 9 月 30 日之前升级基于 Amazon Linux 2 的 Tomcat 8.5 with Corretto 11 版本的平台分支上配置的 Elastic Beanstalk 环境。我们强烈建议您开始计划迁移到当前完全支持的 Tomcat 平台之一：

- Corretto 8 with Tomcat 9 AL2 
- Corretto 11 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 9 AL2023 
- Corretto 17 with Tomcat 10 AL2023

* 我的哪些 Elastic Beanstalk 环境会受到影响？

您可以在 "受影响的资源" 选项卡中查看受影响的 Beanstalk 环境列表。

您还可以使用 Amazon Command Line Interface (Amazon CLI) 命令 “describe-environments" 显示Amazon Linux 2 上使用 Tomcat 8.5 with Corretto 11 配置的所有环境的已筛选列表。 请参考以下示例命令：

aws elasticbeanstalk describe-environments --region cn-north-1 --query "Environments[?PlatformArn.contains(@,'Tomcat 8.5 with Corretto 11 running on 64bit Amazon Linux 2/')]" --output text

更多相关信息，请您参阅 Amazon Elastic Beanstalk 的 Amazon CLI 命令参考[3]。

* 我在哪里可以找到有关 Amazon Elastic Beanstalk 平台支持的更多信息？

即将停用的平台分支版本列于Amazon Elastic Beanstalk平台指南的 "停用平台版本"页面 [4]（这些版本不再列于 "支持的平台"页面 [5] 中）。更多信息，请参考Amazon Elastic Beanstalk 开发人员指南中的 Amazon Elastic Beanstalk 平台支持策略 [2]。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [6]。

[1] https://docs.amazonaws.cn/elasticbeanstalk/latest/dg/platforms-glossary.html
[2] https://docs.amazonaws.cn/elasticbeanstalk/latest/dg/platforms-support-policy.html
[3] https://docs.amazonaws.cn/cli/latest/reference/elasticbeanstalk/describe-environments.html
[4] https://docs.amazonaws.cn/elasticbeanstalk/latest/platforms/platforms-retiring.html
[5] https://docs.amazonaws.cn/elasticbeanstalk/latest/platforms/platforms-supported.html
[6] https://console.amazonaws.cn/support/
'


样例17:
英文原文:
'
You are receiving this notice because you have sent at least one message through SNS that encountered a 429 (Too Many Requests) error in the past 30 days. Currently, when a 429 error is returned by an HTTP/S endpoint, SNS will not retry delivery of the message and will instead fail to deliver to the endpoint. On April 3, 2025, SNS will be updating how 429 errors are handled and will instead retry delivery in accordance with any HTTP/S delivery policy you have defined on your topic or subscription [1].

If an HTTP/S delivery policy is not defined, SNS will attempt to deliver the message up to 3 times by default.

If you have questions or concerns, please contact AWS Support [2].

[1] https://docs.aws.amazon.com/sns/latest/dg/sns-message-delivery-retries.html#creating-delivery-policy
[2] https://aws.amazon.com/support
'

替换后的英文:
'
You are receiving this notice because you have sent at least one message through Amazon Simple Notification Service（SNS）that encountered a 429 (Too Many Requests) error in the past 30 days. Currently, when a 429 error is returned by an HTTP/S endpoint, Amazon SNS will not retry delivery of the message and will instead fail to deliver to the endpoint. On April 3, 2025, Amazon SNS will be updating how 429 errors are handled and will instead retry delivery in accordance with any HTTP/S delivery policy you have defined on your topic or subscription [1].

If an HTTP/S delivery policy is not defined, Amazon SNS will attempt to deliver the message up to 3 times by default.

If you have any questions or concerns, please contact Amazon Web Services China Support [2].

[1] https://docs.amazonaws.cn/en_us/sns/latest/dg/sns-message-delivery-retries.html#creating-delivery-policy
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
您收到此通知是因为您在过去30天内通过Amazon Simple Notification Service（SNS）发送的至少一条消息遇到了429（请求过多）错误。目前，当HTTP/S端点返回429错误时，Amazon SNS不会重试消息的投递，而是会直接失败，无法将消息投递至该端点。2025年4月3日起，Amazon SNS 将更新处理429错误的方式，并将根据您在主题或订阅中定义的任何HTTP/S投递策略进行重试投递[1]。

如果未定义HTTP/S投递策略，Amazon SNS将默认尝试投递该消息最多3次。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队[2]。

[1] https://docs.amazonaws.cn/sns/latest/dg/sns-message-delivery-retries.html#creating-delivery-policy
[2] https://console.amazonaws.cn/support
'


样例18:
英文原文:
'
If you have already upgraded your Amazon SageMaker Studio from Studio Classic to the latest version, you can disregard this notification and stop reading the rest of the message.

We are reaching out to inform you that SageMaker Studio Classic JupyterLab 3 notebooks are reaching EOL on September 10, 2025. Please take the appropriate actions to migrate off JupyterLab 3 notebooks. Please note that the JupyterLab 3 has already reached its end of maintenance date on May 15, 2024 [1]. After September 10, 2025, SageMaker will no longer provide fixes for critical issues for Studio Classic notebooks on JupyterLab 3. 

A list of your affected resource(s) can be found in the 'Affected resources' tab of your AWS Health Dashboard .

Additionally, you will no longer be able to create new notebooks, or restart existing notebooks on SageMaker Studio Classic. You can continue to access existing SageMaker Studio Classic notebooks running on JupyterLab 3 and manually stop them using the SageMaker API ‘delete-app’ through the AWS console, Studio UI, or programmatically via the AWS SDK.

Prior to September 10, 2025, we recommend that you migrate your workloads to the new SageMaker Studio, which supports the latest JupyterLab 4 [2]. The new SageMaker Studio offers enhanced features and capabilities compared to the SageMaker Studio Classic apps. Our team is available to assist you with the migration process and ensure a smooth transition to the new SageMaker Studio [3].

If you have questions or would like additional assistance, please contact AWS Support [4].

[1] https://blog.jupyter.org/jupyterlab-3-end-of-maintenance-879778927db2
[2] https://docs.aws.amazon.com/sagemaker/latest/dg/studio-updated.html
[3] https://docs.aws.amazon.com/sagemaker/latest/dg/studio-updated-migrate.html
[4] https://aws.amazon.com/support
'


替换后的英文:
'

If you have already upgraded your Amazon SageMaker Studio from Studio Classic to the latest version, you can disregard this notification and stop reading the rest of the message.

We are reaching out to inform you that SageMaker Studio Classic JupyterLab 3 notebooks are reaching EOL on September 10, 2025. Please take the appropriate actions to migrate off JupyterLab 3 notebooks. Please note that the JupyterLab 3 has already reached its end of maintenance date on May 15, 2024. After September 10, 2025, we will no longer provide fixes for critical issues for Studio Classic notebooks on JupyterLab 3.

A list of your affected resource(s) can be found in the 'Affected resources' tab.

Additionally, you will no longer be able to create new notebooks, or restart existing notebooks on SageMaker Studio Classic. You can continue to access existing SageMaker Studio Classic notebooks running on JupyterLab 3 and manually stop them using the SageMaker API ‘delete-app’ through the Amazon Web Services console, Studio UI, or programmatically via the Amazon SDK.

Prior to September 10, 2025, we recommend that you migrate your workloads to the new SageMaker Studio, which supports the latest JupyterLab 4 [1]. The new SageMaker Studio offers enhanced features and capabilities compared to the SageMaker Studio Classic apps. Our team is available to assist you with the migration process and ensure a smooth transition to the new SageMaker Studio [2].

If you have any questions or concerns, please reach out to Amazon Web Services China Support [3].

[1] https://docs.amazonaws.cn/en_us/sagemaker/latest/dg/studio-updated.html
[2] https://docs.amazonaws.cn/en_us/sagemaker/latest/dg/studio-updated-migrate.html
[3] https://console.amazonaws.cn/support
'

翻译示例:
'
如果您已经将 Amazon SageMaker Studio 从经典版升级到新版本，您可以忽略此通知并停止阅读其余的信息。

我们特此通知您，SageMaker Studio Classic JupyterLab 3 笔记本将于 2025 年 9 月 10 日结束生命周期。请您采取适当的措施将笔记本从 JupyterLab 3 迁移出来。请注意，JupyterLab 3 已于 2024 年 5 月 15 日终止维护。2025 年 9 月 10 日之后，我们将不再为 JupyterLab 3 上 Studio 经典版笔记本的关键问题提供修复。 

您可以在 “受影响的资源” 选项卡中查看受影响的资源。

另外，您将无法在 SageMaker Studio 经典版上创建新笔记本或重启现有笔记本。您可以继续访问在 JupyterLab 3 上运行的现有 SageMaker Studio 经典版笔记本，并通过亚马逊云科技控制台、Studio UI 或 Amazon SDK 以编程方式使用 SageMaker API “delete-app” 手动停止它们。

在 2025 年 9 月 10 日之前，我们建议您将工作负载迁移到新的 SageMaker Studio，它支持最新的 JupyterLab 4 [1]。与 SageMaker Studio 经典版应用程序相比，新版 SageMaker Studio 提供了更强的特性和功能。我们的团队将协助您完成迁移过程，确保您顺利过渡到新的 SageMaker Studio [2]。

如果您有任何问题或者疑虑，请联系亚马逊云科技中国支持团队 [3]。

[1] https://docs.amazonaws.cn/sagemaker/latest/dg/studio-updated.html
[2] https://docs.amazonaws.cn/sagemaker/latest/dg/studio-updated-migrate.html
[3] https://console.amazonaws.cn/support
'


样例19:
英文原文:
'
We are contacting you as we have identified that your AWS Account currently has one or more AWS Lambda functions using the Node.js 18 runtime.

We are ending support for Node.js 18 in Lambda on September 1, 2025. This follows Node.js 18 End-Of-Life (EOL) reached on April 30, 2025 [1]. End of support does not impact function execution. Your functions will continue to run. However, they will be running on an unsupported runtime which is no longer maintained or patched by the AWS Lambda team.

As described in the Lambda runtime support policy [2], end of support for language runtimes in Lambda happens in several stages. 

- Starting on September 1, 2025, Lambda will no longer apply security patches and other updates to the Node.js 18 runtime used by Lambda functions, and functions using Node.js 18 will no longer be eligible for technical support. Also, Node.js 18 will no longer be available in the AWS Console, although you can still create and update functions using Node.js 18 via AWS CloudFormation, the AWS CLI, AWS Serverless Application Model (SAM), or other tools. 

- Starting October 1, 2025, you will no longer be able to create new Lambda functions using the Node.js 18 runtime. 

- Starting November 1, 2025, you will no longer be able to update existing functions using the Node.js 18 runtime.

We recommend that you upgrade your existing Node.js 18 functions to the latest available Node.js runtime in Lambda before September 1, 2025.

Your impacted Lambda functions using the Node.js 18 runtime are listed on the 'Affected resources' tab of your AWS Health Dashboard.

This notification is generated for functions using the Node.js 18 runtime for the $LATEST function version. The following command shows how to use the AWS CLI [3] to list all functions in a specific region that is using Node.js 18, including published function versions. To find all such functions in your account, repeat the following command for each region:

aws lambda list-functions --region us-east-1 --output text --query "Functions[?Runtime=='nodejs18.x'].FunctionArn"

Starting 180 days before deprecation, you can also use Trusted Advisor to identify all functions using the Node.js 18 runtime [4].

If you have any concerns or require further assistance, please contact AWS Support [5].

[1] https://github.com/nodejs/Release
[2] https://docs.aws.amazon.com/lambda/latest/dg/runtime-support-policy.html
[3] https://aws.amazon.com/cli/
[4] https://docs.aws.amazon.com/awssupport/latest/user/security-checks.html#aws-lambda-functions-deprecated-runtimes
[5] https://aws.amazon.com/support
'

替换后的英文:
'
We are contacting you as we have identified that your Amazon Web Services Account currently has one or more Amazon Lambda functions using the Node.js 18 runtime.

We are ending support for Node.js 18 in Lambda on September 1, 2025. This follows Node.js 18 End-Of-Life (EOL) reached on April 30, 2025 [1]. End of support does not impact function execution. Your functions will continue to run. However, they will be running on an unsupported runtime which is no longer maintained or patched by the Amazon Lambda team.

As described in the Lambda runtime support policy [2], end of support for language runtimes in Lambda happens in several stages. 

- Starting on September 1, 2025, Lambda will no longer apply security patches and other updates to the Node.js 18 runtime used by Lambda functions, and functions using Node.js 18 will no longer be eligible for technical support. Also, Node.js 18 will no longer be available in the Amazon Web Services Console, although you can still create and update functions using Node.js 18 via Amazon CloudFormation, the Amazon Command Line Interface (Amazon CLI), Amazon Serverless Application Model (Amazon SAM), or other tools. 

- Starting October 1, 2025, you will no longer be able to create new Lambda functions using the Node.js 18 runtime. 

- Starting November 1, 2025, you will no longer be able to update existing functions using the Node.js 18 runtime.

We recommend that you upgrade your existing Node.js 18 functions to the latest available Node.js runtime in Lambda before September 1, 2025.

Your impacted Lambda functions using the Node.js 18 runtime are listed on the 'Affected resources' tab.

This notification is generated for functions using the Node.js 18 runtime for the $LATEST function version. The following command shows how to use the Amazon CLI [3] to list all functions in a specific region that is using Node.js 18, including published function versions. To find all such functions in your account, repeat the following command for each region:

aws lambda list-functions --function-version ALL --region cn-north-1 --output json --query "Functions[?Runtime=='nodejs18.x'].FunctionArn"

If you have any questions or concerns, please contact Amazon Web Services China Support [4].

[1] https://github.com/nodejs/Release
[2] https://docs.amazonaws.cn/en_us/lambda/latest/dg/lambda-runtimes.html#runtime-support-policy
[3] https://www.amazonaws.cn/en/tools/
[4] https://console.amazonaws.cn/support
'

翻译示例:
'
我们联系您是因为我们发现您的亚马逊云科技账户当前存在一个或多个使用 Node.js 18 运行时的 Amazon Lambda 函数。

我们将于 2025 年 9 月 1 日终止对 Lambda 中 Node.js 18 的支持。此前，正如通知 [1] 中所言，Node.js 18 的生命周期将于 2025 年 4 月 30 日结束。支持终止不会影响函数的执行。您的函数将继续运行。然而它们将运行在不受支持的运行时上，即 Amazon Lambda 团队不再对其进行维护或应用补丁。

正如 Lambda 运行时支持策略 [2] 中所述，Lambda 中语言运行时支持的终止将分几个阶段进行。

- 从 2025 年 9 月 1 日开始，Lambda 将不再对 Lambda 函数使用的 Node.js 18 运行时应用安全补丁和其他更新，使用 Node.js 18 的函数将不再有资格获得技术支持。此外，亚马逊云科技控制台中将不再提供 Node.js 18，但您仍然可以通过 Amazon CloudFormation, Amazon Command Line Interface (Amazon CLI), Amazon Serverless Application Model (Amazon SAM) 或其他工具创建和更新使用 Node.js 18 的函数。

- 从 2025 年 10 月 1 日起，您将无法再使用 Node.js 18 运行时创建新的 Lambda 函数。

- 自 2025 年 11 月 1 日起，您将无法使用 Node.js 18 运行时更新现有函数。

我们建议您在 2025 年 9 月 1 日前将现有 Node.js 18 函数升级到 Lambda 中最新可用的 Node.js 运行时。

您可以在 “受影响的资源” 选项卡中查看受影响的 Node.js 18 函数列表。

此通知针对使用 Node.js 18 运行时的 $LATEST 函数版本的函数生成。以下命令显示了如何使用 Amazon CLI [3] 列出特定区域中使用 Node.js 18 的所有函数，包括已发布的函数版本。要查找账户中的所有此类函数，请为每个区域重复执行以下命令：

aws lambda list-functions --function-version ALL --region cn-north-1 --output json --query "Functions[?Runtime=='nodejs18.x'].FunctionArn"

如果您有任何疑问或需要进一步帮助，请联系亚马逊云科技中国支持团队[4]。

[1] https://github.com/nodejs/Release
[2] https://docs.amazonaws.cn/lambda/latest/dg/lambda-runtimes.html#runtime-support-policy
[3] https://www.amazonaws.cn/tools/
[4] https://console.amazonaws.cn/support 
'


样例20:
英文原文:
'
[AWS Health may periodically trigger reminder notifications about this communication if resources remain unresolved.]

You are receiving this message because you have one or more instances running Amazon RDS for PostgreSQL minor versions 13.11, 13.12, 13.13, 13.14, 14.9, 14.10, 14.11, 15.4, 15.5, 15.6, 16.1, or 16.2 that require your attention. Amazon RDS for PostgreSQL minor versions listed above will reach end of standard support on March 31, 2025 12:01 AM PDT. To learn more about the RDS policies related to major and minor version support, please refer to Database Engine Versions section in RDS FAQs [1].

Starting December 9, 2024, 12:01 AM PDT, you will not be able to create new RDS instances with PostgreSQL minor versions 13.11, 13.12, 13.13, 13.14, 14.9, 14.10, 14.11, 15.4, 15.5, 15.6, 16.1, or 16.2 from either the AWS Console or the CLI. RDS will upgrade your databases running versions listed above to PostgreSQL 13.18, 14.15, 15.10, 16.6, or higher during a scheduled maintenance window between March 31, 2025 12:01 AM PDT and April 30, 2025 12:01 AM PDT. Starting on April 30, 2025 12:01 AM PDT, any RDS for PostgreSQL databases still running in the affected minor versions will be upgraded to 13.18, 14.15, 15.10, 16.6, or higher regardless of instances scheduled maintenance window.

We recommend that you take action and upgrade [2] your Amazon RDS for PostgreSQL databases running minor versions 13.11 to 13.14, 14.9 to 14.11, 15.4 to 15.6, and 16.1 to 16.2 to versions 13.18, 14.15, 15.10, 16.6, or higher before March 31, 2025 12:01 AM PDT. Alternatively, you can enable Automatic Minor Version Upgrade [3] to allow Amazon RDS to upgrade your instances.

This is a mandatory engine version upgrade and it requires downtime.

We recommend upgrading these instances to newer minor versions to benefit from patches for known security vulnerabilities, as well as bug fixes, performance improvements, and new functionalities added by the PostgreSQL community. To learn more about documented vulnerabilities of PostgreSQL versions, please visit the PostgreSQL security page [4].

Your Amazon RDS for PostgreSQL instances running minor versions mentioned above are listed in 'Affected Resources' tab of the AWS Health Dashboard.

If you have any questions or concerns, the AWS Support Team is available on AWS re:Post [5] and via AWS Support Center [6].

[1] https://aws.amazon.com/rds/faqs/#Database_Engine_Versions
[2] https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.PostgreSQL.html
[3] https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.Upgrading.html#USER_UpgradeDBInstance.Upgrading.AutoMinorVersionUpgrades
[4] https://www.postgresql.org/support/security
[5] https://repost.aws
[6] http://aws.amazon.com/support
'

替换后的英文:
'
[Amazon Health may periodically trigger reminder notifications about this communication if resources remain unresolved.]

You are receiving this message because you have one or more instances running Amazon Relational Database Service (RDS) for PostgreSQL minor versions 13.11, 13.12, 13.13, 13.14, 14.9, 14.10, 14.11, 15.4, 15.5, 15.6, 16.1, or 16.2 that require your attention. Amazon RDS for PostgreSQL minor versions listed above will reach end of standard support on March 31, 2025 15:01 PM UTC+8. To learn more about the Amazon RDS policies related to major and minor version support, please refer to Database Engine Versions section in Amazon RDS FAQs [1].

Starting December 9, 2024, 15:01 PM UTC+8, you will not be able to create new Amazon RDS instances with PostgreSQL minor versions 13.11, 13.12, 13.13, 13.14, 14.9, 14.10, 14.11, 15.4, 15.5, 15.6, 16.1, or 16.2 from either the Amazon Web Services Console or the Amazon Command Line Interface (Amazon CLI). Amazon RDS will upgrade your databases running versions listed above to PostgreSQL 13.18, 14.15, 15.10, 16.6, or higher during a scheduled maintenance window between March 31, 2025 15:01 PM UTC+8 and April 30, 2025 15:01 PM UTC+8. Starting on April 30, 2025 15:01 PM UTC+8, any Amazon RDS for PostgreSQL databases still running in the affected minor versions will be upgraded to 13.18, 14.15, 15.10, 16.6, or higher regardless of instances scheduled maintenance window.

We recommend that you take action and upgrade [2] your Amazon RDS for PostgreSQL databases running minor versions 13.11 to 13.14, 14.9 to 14.11, 15.4 to 15.6, and 16.1 to 16.2 to versions 13.18, 14.15, 15.10, 16.6, or higher before March 31, 2025 15:01 PM UTC+8. Alternatively, you can enable Automatic Minor Version Upgrade [3] to allow Amazon RDS to upgrade your instances.

This is a mandatory engine version upgrade and it requires downtime.

We recommend upgrading these instances to newer minor versions to benefit from patches for known security vulnerabilities, as well as bug fixes, performance improvements, and new functionalities added by the PostgreSQL community. To learn more about documented vulnerabilities of PostgreSQL versions, please visit the PostgreSQL security page [4].

Your Amazon RDS for PostgreSQL instances running minor versions mentioned above are listed in 'Affected Resources' tab.

If you have questions, please contact Amazon Web Services China Support [5].

[1] https://www.amazonaws.cn/en/rds/faqs/#Database_Engine_Versions
[2] https://docs.amazonaws.cn/en_us/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.PostgreSQL.html
[3] https://docs.amazonaws.cn/en_us/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.Upgrading.html#USER_UpgradeDBInstance.Upgrading.AutoMinorVersionUpgrades
[4] https://www.postgresql.org/support/security
[5] https://console.amazonaws.cn/support/
'

翻译示例:
'
[如果资源仍未解决，Amazon Health Dashboard 可能会定期触发有关此通信的提醒通知。]

您收到此消息是因为您有一个或多个运行 Amazon Relational Database Service (RDS) for PostgreSQL 次要版本 13.11、13.12、13.13、13.14、14.9、14.10、14.11、15.4、15.5、15.6、16.1 或 16.2 的实例需要您注意。上面列出的 Amazon RDS for PostgreSQL 次要版本将于北京时间 2025 年 3 月 31 日下午 15:01 终止标准支持。要了解有关主要和次要版本支持的 Amazon RDS 政策的更多信息，请参阅 Amazon RDS 常见问题解答 [1] 中的数据库引擎版本部分。

从 北京时间 2024 年 12 月 9 日下午 15:01 开始，您将无法从亚马逊云科技控制台或 Amazon Command Line Interface (Amazon CLI) 创建运行 PostgreSQL 次要版本 13.11、13.12、13.13、13.14、14.9、14.10、14.11、15.4、15.5、15.6、16.1 或 16.2 的新 Amazon RDS 实例。Amazon RDS 将在北京时间 2025 年 3 月 31 日下午 15:01 至 北京时间 2025 年 4 月 30 日下午 15:01 之间的计划维护时段内将运行上述版本的数据库升级到 PostgreSQL 13.18、14.15、15.10、16.6 或更高版本。从北京时间 2025 年 4 月 30 日下午 15:01 开始，无论实例计划的维护时段如何，仍在受影响的次要版本中运行的任何 Amazon RDS for PostgreSQL 数据库都将升级到 13.18、14.15、15.10、16.6 或更高版本。

我们建议您采取行动，并在北京时间 2025 年 3 月 31 日下午 15:01 之前将运行次要版本 13.11 到 13.14、14.9 到 14.11、15.4 到 15.6 和 16.1 到 16.2 的 Amazon RDS for PostgreSQL 数据库升级 [2] 到版本 13.18、14.15、15.10、16.6 或更高版本。或者，您可以启用自动次要版本升级 [3] 以允许 Amazon RDS 升级您的实例。

这是强制性的引擎版本升级，需要停机。

我们建议将这些实例升级到较新的次要版本，以便从已知安全漏洞的补丁以及 PostgreSQL 社区添加的错误修复、性能改进和新功能中获益。要了解有关 PostgreSQL 版本记录漏洞的更多信息，请访问 PostgreSQL 安全页面 [4]。

您可以在“受影响的资源”选项卡中查看运行上述次要版本的 Amazon RDS for PostgreSQL 实例。

如果您有任何问题或者疑虑，请联系亚马逊云科技中国支持团队 [5]。

[1] https://www.amazonaws.cn/rds/faqs/#Database_Engine_Versions
[2] https://docs.amazonaws.cn/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.PostgreSQL.html
[3] https://docs.amazonaws.cn/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.Upgrading.html#USER_UpgradeDBInstance.Upgrading.AutoMinorVersionUpgrades
[4] https://www.postgresql.org/support/security
[5] https://console.amazonaws.cn/support/
'


样例21:
英文原文:
'
Hello,

On {{properties.first_notification_sent.value}}, we emailed you about Amazon EC2 {{properties.instance_family.value}} instances reaching end of life on {{properties.eol_date.value}}. EC2 has detected an active {{properties.instance_family.value}} instance (instance-ID: {{properties.instance_id.value}}) associated with your AWS account (AWS Account ID: {{accountEntities.[0].accountId}}) in the {{region}} Region. You need to migrate to a newer generation instance type no later than {{properties.start_time.value}}. Amazon EC2 instance (instance-ID: {{properties.instance_id.value}}) will be stopped on {{properties.start_time.value}}. You will not be able to restart your stopped instance without modifying the instance type as the {{properties.instance_family.value}} instance type configuration is no longer supported.

A list of one or more active {{properties.instance_family.value}} instances can be found in the "Affected Resources" tab of your AWS Health Dashboard.

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the {{properties.instance_family.value}} instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new {{properties.instance_family.value}} instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the EBS volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the EBS volume(s) associated with the stopped instance, you can either delete the EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the {{properties.instance_family.value}} instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. {{properties.instance_family.value}} instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the {{properties.instance_family.value}} instance family. Newer generation instances feature the AWS Nitro System and typically offer better price-performance than {{properties.instance_family.value}} instances.

If you have any questions or concerns, you can reach out to your AWS Accounts Teams or the AWS Support Team [1].

[1] https://aws.amazon.com/support

Sincerely,
Amazon Web Services

Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc. Amazon.com is a registered trademark of Amazon.com, Inc. This message was produced and distributed by Amazon Web Services Inc., 410 Terry Ave. North, Seattle, WA 98109-5210.

Reference: {{eventIdentifier}}
'

替换后的英文:
'
On {{metadata.first_notification_sent.value}}, we emailed you about Amazon Elastic Compute Cloud (Amazon EC2) {{metadata.instance_family.value}} instances reaching end of life on {{metadata.eol_date.value}}. Amazon EC2 has detected an active {{metadata.instance_family.value}} instance (instance-ID: {{metadata.instance_id.value}}) in the {{region}} Region. You need to migrate to a newer generation instance type no later than {{metadata.start_time.value}}. Amazon EC2 instance (instance-ID: {{metadata.instance_id.value}}) will be stopped on {{metadata.start_time.value}}. You will not be able to restart your stopped instance without modifying the instance type as the {{metadata.instance_family.value}} instance type configuration is no longer supported.

A list of one or more active {{metadata.instance_family.value}} instances can be found in the "Affected Resources" tab.

* What will happen to my instance after it has been stopped?
Your instance will be stopped on the specified date. You will not be able to restart your instance after it has been stopped without modifying the instance type as the {{metadata.instance_family.value}} instance type is no longer supported. Additionally, the data on any local instance store volumes will not be preserved when the instance is stopped. You will also not be able to launch new {{metadata.instance_family.value}} instances.

* What do I need to do?
You must migrate to a newer generation instance type before the specified date when the instance will be stopped. Newer generation instances typically offer better price-performance than older generation instances.

* What will happen to the Amazon Elastic Block Store (Amazon EBS) volume(s) and ENI(s) attached to the instance after it has been stopped?
You will be able to detach Amazon EBS volume(s) associated with the instance even after it has been stopped. To stop billing for the Amazon EBS volume(s) associated with the stopped instance, you can either delete the Amazon EBS volume(s) after detaching it from the instance or enable the Delete on termination flag for the EBS volume(s) associated with your instance and terminate your instance. You will be able to detach any secondary ENIs associated with the instance after it has been stopped. You will need to disable the Delete on termination flag for the primary ENI and terminate the instance to be able to the use the primary network interface with another instance.

* Why is the {{metadata.instance_family.value}} instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. {{metadata.instance_family.value}} instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the {{metadata.instance_family.value}} instance family. Newer generation instances feature the Amazon Nitro System and typically offer better price-performance than {{metadata.instance_family.value}} instances.

If you have any questions or concerns, please contact Amazon Web Services China Support [1].

[1] https://console.amazonaws.cn/support/
'

翻译示例:
'
在{{metadata.first_notification_sent.value}}，我们通过电子邮件通知您，Amazon Elastic Compute Cloud (Amazon EC2)的{{metadata.instance_family.value}}实例将在{{metadata.eol_date.value}}停止服务。Amazon EC2检测到活跃的{{metadata.instance_family.value}}实例（实例ID：{{metadata.instance_id.value}}）位于{{region}}区域。您需要在{{metadata.start_time.value}}之前迁移到新一代实例类型。Amazon EC2实例（实例ID：{{metadata.instance_id.value}}）将在{{metadata.start_time.value}}停止。由于{{metadata.instance_family.value}}实例类型配置不再受支持，您在未修改实例类型的情况下将无法重新启动已停止的实例。

您可以在“受影响的资源”选项卡中找到一或多个活跃的{{metadata.instance_family.value}}实例的列表。

我的实例在停止后将发生什么？
您的实例将在指定日期停止。由于{{metadata.instance_family.value}}实例类型不再受支持，您在未修改实例类型的情况下将无法重新启动已停止的实例。此外，当实例停止时，任何本地实例存储卷上的数据将不会被保留。您也将无法启动新的{{metadata.instance_family.value}}实例。

我需要做些什么？
您必须在实例停止的指定日期之前迁移到新一代实例类型。新一代实例通常比旧一代实例提供更好的性价比。

实例停止后，附加到实例的Amazon Elastic Block Store (Amazon EBS)卷和ENI会发生什么？
即使实例已停止，您也可以分离与实例关联的Amazon EBS卷。为了停止对与已停止实例关联的Amazon EBS卷计费，您可以在从实例分离后删除Amazon EBS卷，或者为与您的实例关联的EBS卷启用“删除时终止”标志并终止您的实例。实例停止后，您可以分离与实例关联的任何次要ENI。为了能够将主网络接口用于另一个实例，您需要禁用主ENI的“删除时终止”标志并终止实例。

为什么不再支持{{metadata.instance_family.value}}实例？
Amazon EC2为客户提供可扩展和可靠的体验。由于我们无法继续在{{metadata.instance_family.value}}实例系列上提供您期望的高水平弹性，因此不再支持该实例系列。新一代实例配备了Amazon Nitro系统，并且通常比{{metadata.instance_family.value}}实例提供更好的性价比。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [1]。

[1] https://console.amazonaws.cn/support/
'


样例22:
英文原文:
'
We are contacting you to remind you of an upcoming authorization improvement to the CreateVolume API of Amazon Elastic Block Store (Amazon EBS). Beginning February 17, 2025, we will add authorization for the source snapshots in CreateVolume requests for your account. This authorization change has been enabled for customers on January 31, 2025, except for your account, as we have identified that you have a policy that would be impacted by this change and need to take action. For more details about the launch, please visit our launch blog [1].

With this additional authorization, you will have more granular controls to set resource-level permissions for the creation of a volume and selection of the source snapshot when calling the CreateVolume action [2] in your IAM policy. If you have already added a resource statement using an Amazon Resource Name (ARN) [3] for the volumes being created, you will also need to explicitly specify a resource statement for your snapshot to avoid impact.

What will be changed through this update?
When creating or restoring a volume from a source snapshot, you need to use an IAM policy to grant permissions to perform the CreateVolume action. Previously, the CreateVolume action in your IAM policy required authorization for the volume getting created, and the source snapshot was not part of the resource context of the CreateVolume action in your IAM policy. With this change, you will be able to enforce resource-level permissions for both the volume getting created and the source snapshot. To meet your specific permission requirements on the source snapshots, you can also use any of these 5 EC2-specific condition keys: ec2:Encrypted, ec2:VolumeSize, ec2:Owner, ec2:ParentVolume, and ec2:SnapshotTime. Additionally, you can use global condition keys [4] for the source snapshot in your IAM policy.

Which IAM policies should I update?
We recommend you review your IAM policies that contain the CreateVolume action. In your current policy, you may have specified a principal or multiple principals, with or without condition keys to create a volume from a source snapshot. Ensure your policies are set up to allow or deny access to the source snapshot and volume getting created, or create separate IAM policy statements for each. Policies requiring updates generally fall into the below two categories:

Category 1: You do not have an allow statement for a snapshot resource.
If your policy’s Resource element only matched volumes, then you need to allow the snapshot resource as well. Observe the code example.
{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "arn:aws:ec2:*:*:volume/*"
}
The updated code block would become the following.
{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws:ec2:*:*:volume/*"
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

See a similar example of an AWS managed policy: AWSElasticDisasterRecoveryServiceRolePolicy [5]

Category 2: Your resource element allows both volumes and snapshots, but your condition only applies to a volume.
If your policy’s Resource element uses a wildcard to match both volumes and snapshots and includes Condition elements that are specific to a volume, then you must modify the Resource element to apply only to volumes and add a new statement to authorize snapshots.
Here is an example of a previous policy that will not work after this change.

{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "*",
    "Condition": {
        "StringLike": {
            "aws:RequestTag/CSIVolumeName": "*"
        }
    }
}


To update the policy and make it work, it would become the following.
{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws:ec2:*:*:volume/*",
      "Condition": {
        "StringLike": {
          "aws:RequestTag/CSIVolumeName": "*"
        }
      }
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

See a similar example of an AWS managed policy: AmazonEBSCSIDriverPolicy [6]


What is the timeline to complete my policy update?
Most AWS users aren’t impacted by this update. We have previously notified you by email or through your AWS Health Dashboard if we detected that you have a policy that would be impacted by this change. Impacted accounts are currently allowlisted, and your previous policy will remain in place until February 17, 2025. On February 17, 2025, AWS will start removing accounts from the allowlist as you update your IAM policies with May 17, 2025 being the final deadline for all accounts to migrate to the new permission model. After May 17, 2025, your requests to invoke CreateVolume API may be denied if you have not taken any action to update your policy. See below:

•    February 17, 2025: AWS begins removing accounts from the allowlist, starting with users who have updated your IAM policies.
•    May 17, 2025: Final deadline for all accounts to migrate to the new permission model.

How do I validate if my IAM policy update is correct?
If you need to validate that your IAM policies are correct, then use the DryRun mode to test that your changes to IAM policies are correct. The DryRun mode allows you to see the authorization result under the new permission model even if your actual APIs are allowlisted to use the old permission model. To use DryRun, specify the --dry-run parameter in AWS Command Line Interface (AWS CLI) commands or Amazon EBS CreateVolume [2] API calls. Your CreateVolume API calls that use DryRun always returns an error response [7]. If you receive an error with DryRunOperation, then this means your policy works with the new permission model. If you receive an error with UnauthorizedOperation, then you must make an update on your IAM policies. Refer to the CLI documentation [8] to find out more about the DryRun mode. 
If you need assistance with updating your IAM policies or have any further questions regarding this change, please reach out to the AWS Support [9].


[1] https://aws.amazon.com/blogs/storage/enhancing-resource-level-permission-for-creating-an-amazon-ebs-volume-from-a-snapshot/
[2] https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateVolume.html
[3] https://docs.aws.amazon.com/IAM/latest/UserGuide/reference-arns.html
[4] https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_condition-keys.html
[5] https://docs.aws.amazon.com/aws-managed-policy/latest/reference/AWSElasticDisasterRecoveryServiceRolePolicy.html
[6] https://docs.aws.amazon.com/aws-managed-policy/latest/reference/AmazonEBSCSIDriverPolicy.html
[7]https://docs.aws.amazon.com/AWSEC2/latest/APIReference/API_CreateVolume.html#:~:text=Required%3A%20No-,DryRun,-Checks%20whether%20you
[8] https://docs.aws.amazon.com/cli/latest/userguide/cli-usage-help.html
[9] https://aws.amazon.com/support
'

替换后的英文:
'
We are contacting you to remind you of an upcoming authorization improvement to the CreateVolume API of Amazon Elastic Block Store (EBS). Beginning February 17, 2025, we will add authorization for the source snapshots in CreateVolume requests for your account. This authorization change has been enabled for customers on January 31, 2025, except for your account, as we have identified that you have a policy that would be impacted by this change and need to take action. For more details about the launch, please visit our launch blog [1].

With this additional authorization, you will have more granular controls to set resource-level permissions for the creation of a volume and selection of the source snapshot when calling the CreateVolume action [2] in your Amazon Identity and Access Management (IAM)  policy. If you have already added a resource statement using an Amazon Resource Name (ARN) [3] for the volumes being created, you will also need to explicitly specify a resource statement for your snapshot to avoid impact.

What will be changed through this update?
When creating or restoring a volume from a source snapshot, you need to use an IAM policy to grant permissions to perform the CreateVolume action. Previously, the CreateVolume action in your IAM policy required authorization for the volume getting created, and the source snapshot was not part of the resource context of the CreateVolume action in your IAM policy. With this change, you will be able to enforce resource-level permissions for both the volume getting created and the source snapshot. To meet your specific permission requirements on the source snapshots, you can also use any of these 5 Amazon Elastic Compute Cloud (EC2)-specific condition keys: ec2:Encrypted, ec2:VolumeSize, ec2:Owner, ec2:ParentVolume, and ec2:SnapshotTime. Additionally, you can use global condition keys [4] for the source snapshot in your IAM policy.

Which IAM policies should I update?
We recommend you review your IAM policies that contain the CreateVolume action. In your current policy, you may have specified a principal or multiple principals, with or without condition keys to create a volume from a source snapshot. Ensure your policies are set up to allow or deny access to the source snapshot and volume getting created, or create separate IAM policy statements for each. Policies requiring updates generally fall into the below two categories:

Category 1: You do not have an allow statement for a snapshot resource.
If your policy’s Resource element only matched volumes, then you need to allow the snapshot resource as well. Observe the code example.
{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "arn:aws-cn:ec2:*:*:volume/*"
}
The updated code block would become the following.
{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*:*:volume/*"
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

Category 2: Your resource element allows both volumes and snapshots, but your condition only applies to a volume.
If your policy’s Resource element uses a wildcard to match both volumes and snapshots and includes Condition elements that are specific to a volume, then you must modify the Resource element to apply only to volumes and add a new statement to authorize snapshots.
Here is an example of a previous policy that will not work after this change.

{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "*",
    "Condition": {
        "StringLike": {
            "aws:RequestTag/CSIVolumeName": "*"
        }
    }
}

To update the policy and make it work, it would become the following.
{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*:*:volume/*",
      "Condition": {
        "StringLike": {
          "aws:RequestTag/CSIVolumeName": "*"
        }
      }
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

What is the timeline to complete my policy update?
Most Amazon Web Services users aren’t impacted by this update. We have previously notified you by email or through your Amazon Health Dashboard if we detected that you have a policy that would be impacted by this change. Impacted accounts are currently allowlisted, and your previous policy will remain in place until February 17, 2025. On February 17, 2025, Amazon Web Services will start removing accounts from the allowlist as you update your IAM policies with May 17, 2025 being the final deadline for all accounts to migrate to the new permission model. After May 17, 2025, your requests to invoke CreateVolume API may be denied if you have not taken any action to update your policy. See below:

•    February 17, 2025: Amazon Web Services begins removing accounts from the allowlist, starting with users who have updated your IAM policies.
•    May 17, 2025: Final deadline for all accounts to migrate to the new permission model.

How do I validate if my IAM policy update is correct?
If you need to validate that your IAM policies are correct, then use the DryRun mode to test that your changes to IAM policies are correct. The DryRun mode allows you to see the authorization result under the new permission model even if your actual APIs are allowlisted to use the old permission model. To use DryRun, specify the --dry-run parameter in Amazon Command Line Interface (Amazon CLI) commands or Amazon EBS CreateVolume [2] API calls. Your CreateVolume API calls that use DryRun always returns an error response [7]. If you receive an error with DryRunOperation, then this means your policy works with the new permission model. If you receive an error with UnauthorizedOperation, then you must make an update on your IAM policies. Refer to the Amazon CLI documentation [8] to find out more about the DryRun mode. 
If you have any questions or concerns, please contact Amazon Web Services China Support [7].

[1] https://aws.amazon.com/blogs/storage/enhancing-resource-level-permission-for-creating-an-amazon-ebs-volume-from-a-snapshot/
[2] https://docs.amazonaws.cn/en_us/AWSEC2/latest/APIReference/API_CreateVolume.html
[3] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/reference-arns.html
[4] https://docs.amazonaws.cn/en_us/IAM/latest/UserGuide/reference_policies_condition-keys.html
[5] https://docs.amazonaws.cn/en_us/AWSEC2/latest/APIReference/API_CreateVolume.html#:~:text=Required%3A%20No-,DryRun,-Checks%20whether%20you
[6] https://docs.amazonaws.cn/cli/latest/userguide/cli-usage-help.html
[7] https://console.amazonaws.cn/support/
'

翻译示例:
'
我们与您联系，是为了提醒您即将对 Amazon Elastic Block Store (EBS) 的 CreateVolume API 进行的授权改进。从 2025 年 2 月 17 日起，我们将在您的帐户的 CreateVolume 请求中添加对源快照的授权。我们已于 2025 年 1 月 31 日为客户启用了此授权变更，但您的帐户除外，因为我们已确定您的策略会受到此变更的影响，因此需要采取行动。有关变更的更多详细信息，请访问我们的博客 [1]。

有了这一附加授权，您就可以在调用 Amazon Identity and Access Management (IAM)策略中的 CreateVolume 操作 [2] 时，更精细的控制为创建卷和选择源快照设置资源级权限。如果您已经使用Amazon资源名称 (ARN) [3] 为正在创建的卷添加了资源声明，则还需要为快照明确指定资源声明以避免影响。

本次更新将改变什么？
从源快照创建或恢复卷时，需要使用 IAM 策略授予执行 CreateVolume 操作的权限。以前，IAM 策略中的 CreateVolume 操作需要对创建的卷进行授权，而源快照不属于 IAM 策略中 CreateVolume 操作的资源上下文。有了这项更改，您就可以对创建的卷和源快照执行资源级权限。为了满足您对源快照的特定权限要求，您还可以使用以下 5 个Amazon Elastic Compute Cloud (EC2)特定条件密钥：ec2:Encrypted、ec2:VolumeSize、ec2:Owner、ec2:ParentVolume 和 ec2:SnapshotTime。此外，您还可以在 IAM 策略中为源快照使用全局条件密钥 [4]。

我应该更新哪些 IAM 策略？
我们建议您查看包含 CreateVolume 操作的 IAM 策略。在您当前的策略中，您可能指定了一个或多个principals委托，（有或没有条件密钥）来从源快照创建卷。请确保您的策略已设置为允许或拒绝访问源快照和正在创建的卷，或为每个卷创建单独的 IAM 策略声明。需要更新的策略一般分为以下两类：

类别 1：您没有快照资源的允许声明。
如果您的策略的 “Resource”元素只匹配卷，那么您也需要允许快照资源。请观察策略示例。

{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "arn:aws-cn:ec2:*:*:volume/*"
}
更新后的策略如下：
{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*:*:volume/*"
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

类别 2：您的资源元素允许卷和快照，但条件只适用于卷。
如果策略的 “Resource”元素使用通配符同时匹配卷和快照，并包含特定于卷的 “Resource”元素，则必须修改 “Resource”元素使其仅适用于卷，并添加新语句以授权快照。
下面是一个之前策略的示例，该策略在更改后将无法使用。

{
    "Effect": "Allow",
    "Action": "ec2:CreateVolume",
    "Resource": "*",
    "Condition": {
        "StringLike": {
            "aws:RequestTag/CSIVolumeName": "*"
        }
    }
}

为了更新该政策并使其发挥作用，它将变成如下内容。

{
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*:*:volume/*",
      "Condition": {
        "StringLike": {
          "aws:RequestTag/CSIVolumeName": "*"
        }
      }
    },
    {
      "Effect": "Allow",
      "Action": "ec2:CreateVolume",
      "Resource": "arn:aws-cn:ec2:*::snapshot/snap-1234567890abcdef0"
    }
  ]
}

完成策略更新的期限是什么？
大多数亚马逊云科技用户不受此更新的影响。如果我们检测到您的策略会受到此更改的影响，我们会通过电子邮件或 Amazon Health Dashboard 通知您。受影响的帐户目前在允许列表中，您之前的策略将在 2025 年 2 月 17 日之前保持不变。2025 年 2 月 17 日，随着您更新 IAM 策略，亚马逊云科技 将开始从允许列表中移除帐户，2025 年 5 月 17 日是所有帐户迁移到新权限模型的最后期限。2025 年 5 月 17 日之后，如果您没有采取任何行动更新策略，调用 CreateVolume API 的请求可能会被拒绝。请参见下文：

• 2025 年 2 月 17 日： 亚马逊云科技开始从允许列表中移除帐户，从更新了 IAM 策略的用户开始。
• 2025 年 5 月 17 日： 所有账户迁移到新权限策略的最后截止日期。

如何验证 IAM 策略更新是否正确？
如果您需要验证 IAM 策略是否正确，那么请使用 “DryRun ”模式来测试您对 IAM 策略所做的更改是否正确。DryRun 模式允许您查看新权限模型下的授权结果，即使您的实际 API 允许列表使用的是旧权限模型。要使用 DryRun，请在 Amazon Command Line Interface (Amazon CLI) 命令或 Amazon EBS CreateVolume [2] API 调用中指定 --dry-run 参数。使用 DryRun 的 CreateVolume API 调用总是会返回错误响应 [5]。如果您收到 DryRunOperation 错误，则表示您的策略可与新的权限模型配合使用。如果收到 UnauthorizedOperation 错误，则必须更新 IAM 策略。请参阅 Amazon CLI 文档 [6]，了解有关 DryRun 模式的更多信息。
如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [5]。

[1] https://aws.amazon.com/blogs/storage/enhancing-resource-level-permission-for-creating-an-amazon-ebs-volume-from-a-snapshot/
[2] https://docs.amazonaws.cn/AWSEC2/latest/APIReference/API_CreateVolume.html
[3] https://docs.amazonaws.cn/IAM/latest/UserGuide/reference-arns.html
[4] https://docs.amazonaws.cn/IAM/latest/UserGuide/reference_policies_condition-keys.html
[5] https://docs.amazonaws.cn/en_us/AWSEC2/latest/APIReference/API_CreateVolume.html#:~:text=Required%3A%20No-,DryRun,-Checks%20whether%20you
[6] https://docs.amazonaws.cn/cli/latest/userguide/cli-usage-help.html
[7] https://console.amazonaws.cn/support/
'


样例23:
英文原文:
'
We are contacting you because of an upcoming change to Patch Manager, a capability of AWS Systems Manager (SSM) that requires you to change how you are calling the service. 

Currently, Patch Manager allows either a 20-character ID like "pb-0e392de35e7c563bz" or a full Amazon Resource Name (ARN) [1] for requests made to SSM Patch Manager with “BaselineId” fields. This is the allowable format of Patch Baseline “BaselineId” parameters:

• pb-[0-9a-f]{17}
• arn:aws-partition:ssm:region:account-id:patchbaseline/patch-baseline-id

Patch Manager also supports two APIs that accept an “InstanceId” or “InstanceIds” field. For these APIs, an instance or managed node resource id is accepted in any of the following formats:

• i-[0-9a-f]{8}
• i-[0-9a-f]{17}
• mi-[0-9a-f]{17}

Starting January 22, 2025, we will begin validating this format on ARNs or resource ids passed as parameters for the following APIs:

• GetPatchBaseline - BaselineId

• UpdatePatchBaseline - BaselineId

• RegisterDefaultPatchBaseline - BaselineId

• RegisterPatchBaselineForPatchGroup - BaselineId

• DeregisterPatchBaselineForPatchGroup - BaselineId

• DescribeEffectivePatchesForPatchBaseline - BaselineId

• DeletePatchBaseline - BaselineId

• DescribeInstancePatches - InstanceId

• DescribeInstancePatchStates - InstanceIds

After January 22, 2025, calls you make to any of the above APIs that use invalid ARNs or resource ids may fail with an error as below:

" An error occurred (AccessDeniedException) when calling the GetPatchBaseline operation: One or more request parameters are invalid. Invalid ARN."

We detected you made a request that is invalid against the above format SSM Patch Manager APIs within the last 30 days. You can find the list of affected request ids in the "Affected Resources" of your AWS Health Dashboard. To find the actual request parameters that are invalid against the new format, you can check your CloudTrail event history and filter the EventId with an affected request id [2].

You need to modify the ARNs you are passing to conform to the allowable format. Please refer to our public documentation for ARN formats allowable by SSM [3]. Any calls you make with a 20 character baseline id will not be affected.

If you have any further questions or concerns, please contact AWS Support [4].

[1] https://docs.aws.amazon.com/systems-manager/latest/APIReference/API_GetPatchBaseline.html#API_GetPatchBaseline_RequestSyntax 
[2] https://docs.aws.amazon.com/awscloudtrail/latest/userguide/view-cloudtrail-events.html 
[3] https://docs.aws.amazon.com/systems-manager/latest/userguide/security_iam_service-with-iam.html 
[4] https://aws.amazon.com/support
'

替换后的英文:
'
We are contacting you because of an upcoming change to Patch Manager, a capability of Amazon Systems Manager that requires you to change how you are calling the service. 

Currently, Patch Manager allows either a 20 character ID like pb-0e392de35e7c563bz or a full Amazon Resource Name (ARN) [1] for requests made to Systems Manager Patch Manager with “BaselineId” fields. This is the allowable format of Patch Baseline “BaselineId” parameters:

- pb-[0-9a-f]{17}
- arn:aws-partition:ssm:region:account-id:patchbaseline/patch-baseline-id

Patch Manager also supports two APIs that accept an “InstanceId” or “InstanceIds” field. For these APIs, an instance or managed node resource id is accepted in any of the following formats:

- i-[0-9a-f]{8}
- i-[0-9a-f]{17}
- mi-[0-9a-f]{17}

Starting March 31, 2025, we will begin validating this format on ARNs or resource ids passed as parameters for the following APIs:

- GetPatchBaseline - BaselineId
- UpdatePatchBaseline - BaselineId
- RegisterDefaultPatchBaseline - BaselineId
- RegisterPatchBaselineForPatchGroup - BaselineId
- DeregisterPatchBaselineForPatchGroup - BaselineId
- DescribeEffectivePatchesForPatchBaseline - BaselineId
- DeletePatchBaseline - BaselineId
- DescribeInstancePatches - InstanceId
- DescribeInstancePatchStates - InstanceIds

After March 31, 2025, calls you make to any of the above APIs that use invalid ARNs or resource ids may fail with an error as follows:

An error occurred (AccessDeniedException) when calling the GetPatchBaseline operation: One or more request parameters are invalid. Invalid ARN.

We detected you made a request that is invalid against the above format to one or more Systems Manager Patch Manager APIs within the last 60 days. You need to modify the ARNs you are passing to avoid impact to your usage of Systems Manager. 

You can find the list of API calls you made that were flagged in the "Affected Resources" tab in the format: 'Request Id | Event Type | Date Called'. 

We detected applicable request(s) made in the following region(s): {{region}}

To find the actual request parameters that are invalid against the new format, you can download your CloudTrail event history from the CloudTrail event history console page and filter the events using the “requestID” field [2]. Alternatively, if you have configured an event data store for your CloudTrail management events, you can perform a query using the provided capabilities [3].

You need to modify the ARNs you are passing to these APIs to conform to the allowable format. Please refer to our public documentation for ARN formats allowable by Systems Manager [4]. Any calls you make with a 20 character baseline id will not be affected.

If you have any questions or concerns, please reach out to Amazon Web Services China Support [5]. 

[1] https://docs.amazonaws.cn/en_us/systems-manager/latest/APIReference/API_GetPatchBaseline.html#API_GetPatchBaseline_RequestSyntax
[2] https://docs.amazonaws.cn/en_us/awscloudtrail/latest/userguide/view-cloudtrail-events.html
[3] https://docs.amazonaws.cn/en_us/awscloudtrail/latest/userguide/query-event-data-store-cloudtrail.html
[4] https://docs.amazonaws.cn/en_us/systems-manager/latest/userguide/security_iam_service-with-iam.html
[5] https://console.amazonaws.cn/support
'

翻译示例:
'
我们联系您是因为即将对补丁管理器（Amazon Systems Manager的一个功能）进行更改，这需要您更改调用该服务的方式。

目前，补丁管理器允许在对 Systems Manager 补丁管理器的请求中使用“BaselineId”字段时，使用类似“pb-0e392de35e7c563bz”的20个字符 ID 或者完整的 Amazon Resource Name (ARN)[1]。补丁基准“BaselineId”参数的允许格式如下：

- pb-[0-9a-f]{17}
- arn:aws-partition:ssm:region:account-id:patchbaseline/patch-baseline-id

补丁管理器还支持两个接受“InstanceId”或“InstanceIds”字段的 API。对于这些 API，实例或托管节点资源 ID 可以使用以下格式之一：

- i-[0-9a-f]{8}
- i-[0-9a-f]{17}
- mi-[0-9a-f]{17}

从2025年3月31日起，我们将开始验证以下 API 中作为参数传递的 ARN 或资源 ID 的格式：

- GetPatchBaseline - BaselineId
- UpdatePatchBaseline - BaselineId
- RegisterDefaultPatchBaseline - BaselineId
- RegisterPatchBaselineForPatchGroup - BaselineId
- DeregisterPatchBaselineForPatchGroup - BaselineId
- DescribeEffectivePatchesForPatchBaseline - BaselineId
- DeletePatchBaseline - BaselineId
- DescribeInstancePatches - InstanceId
- DescribeInstancePatchStates - InstanceIds

从2025年3月31日之后，您对上述任何 API 的调用如果使用了无效的 ARN 或资源 ID，可能会失败并出现如下错误：

An error occurred (AccessDeniedException) when calling the GetPatchBaseline operation: One or more request parameters are invalid. Invalid ARN.

我们检测到您在过去60天内对一个或多个 Systems Manager 补丁管理器API发出了不符合上述格式的请求。您需要修改传递的 ARN 以避免影响 Systems Manager 的使用。

您可以在“受影响的资源”下找到被标记的API调用列表，格式为：“请求 ID | 事件类型 | 调用日期”。

我们检测到适用的请求是从以下区域发出的：{{region}}

要查找与新格式不符的实际请求参数，您可以从 Amazon CloudTrail 事件历史记录控制台页面下载您的 CloudTrail 事件历史，并使用 “requestID” 字段过滤事件[2]。或者，如果您已为 CloudTrail 管理事件配置了事件数据存储，您可以使用提供的功能执行查询[3]。

您需要修改传递的ARN以符合允许的格式。请参阅我们的公共文档以了解 Systems Manager 允许的 ARN 格式[4]。您使用 20 个字符的基线 ID 进行的任何调用都不会受到影响。

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队[5]。

[1] https://docs.amazonaws.cn/systems-manager/latest/APIReference/API_GetPatchBaseline.html#API_GetPatchBaseline_RequestSyntax
[2] https://docs.amazonaws.cn/awscloudtrail/latest/userguide/view-cloudtrail-events.html
[3] https://docs.amazonaws.cn/awscloudtrail/latest/userguide/query-event-data-store-cloudtrail.html
[4] https://docs.amazonaws.cn/systems-manager/latest/userguide/security_iam_service-with-iam.html
[5] https://console.amazonaws.cn/support/
'


样例24:
英文原文:
'
We first launched the EC2 P3 instances ("P3 instances") in October 2017 and have continued to provide customers a scalable AWS experience on the P3 instances. However, we can no longer continue to offer the high level of elasticity you have come to expect on the P3 instance family and are therefore working with customers to migrate their workloads to newer generation EC2 G or P instances by December 20, 2025. To learn more about these instances, see our documentation [1].

Existing users of P3 instances will see no change in your experience leading up to the retirement date of December 20, 2025. However, starting March 1, 2025, customers will no longer be able to purchase new Reserved Instances or Instance Savings Plans for P3 instances. Going forward, only existing P3 customers who have run P3 instances within the past 12 months can restart or launch new P3 instances. Please reach out to AWS Support [2] for any questions or read our FAQs.

FAQs

* Why is the P3 instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. P3 instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the P3 instance family.

* What do I need to do?
Please migrate to newer generation EC2 G or P instances by December 20, 2025. Newer generation instances are expected to offer better price-performance than older generation instances.

* Who will have access to the P3 instances going forward?
Only existing P3 customers who have run P3 instances within the past 12 months can restart or launch new P3 instances.

* What happens to my existing RIs and SPs?
You can exchange P3 Convertible Reserved Instances (CRI) with any other EC2 instance CRI using the Amazon EC2 console or command line tool by December 20, 2025. For P3 CRI, we recommend exchanging with newer generation EC2 G or P instances. For P3 Standard Reserved Instances or P3 Instance Savings Plans with an expiration date after December 20, 2025, you can reach out to Customer Service through AWS Support center [2] to request cancellation and pro-rated refunds.

* Can I purchase new RIs and SPs?
Starting March 1, 2025, you will no longer be able to purchase new Standard Reserved Instances or Instance Savings Plans for P3 instances. New Convertible Reserved Instances and Compute Savings Plans will not be applicable for P3 instances starting March 1, 2025.

[1] https://aws.amazon.com/ec2/instance-types/#Accelerated_Computing
[2] https://aws.amazon.com/support
'

替换后的英文:
'
We first launched the Amazon Elastic Compute Cloud (EC2) P3 instances ("P3 instances") in October 2017 and have continued to provide customers a scalable experience on the P3 instances. However, we can no longer continue to offer the high level of elasticity you have come to expect on the P3 instance family and are therefore working with customers to migrate their workloads to newer generation Amazon EC2 G or P instances by December 20, 2025. To learn more about these instances, see our documentation [1].

Existing users of P3 instances will see no change in your experience leading up to the retirement date of December 20, 2025. However, starting March 1, 2025, customers will no longer be able to purchase new Reserved Instances or Instance Savings Plans for P3 instances. Going forward, only existing P3 customers who have run P3 instances within the past 12 months can restart or launch new P3 instances. If you have any questions or concerns, please contact Amazon Web Services China Support [2] or read our FAQs.

FAQs

* Why is the P3 instance no longer supported?
Amazon EC2 provides customers a scalable and reliable experience. P3 instance is not supported because we can no longer continue to offer the high level of elasticity you have come to expect on the P3 instance family.

* What do I need to do?
Please migrate to newer generation Amazon EC2 G or P instances by December 20, 2025. Newer generation instances are expected to offer better price-performance than older generation instances.

* Who will have access to the P3 instances going forward?
Only existing P3 customers who have run P3 instances within the past 12 months can restart or launch new P3 instances.

* What happens to my existing RIs and SPs?
You can exchange P3 Convertible Reserved Instances (CRI) with any other Amazon EC2 instance CRI using the Amazon EC2 console or command line tool by December 20, 2025. For P3 CRI, we recommend exchanging with newer generation Amazon EC2 G or P instances. For P3 Standard Reserved Instances or P3 Instance Savings Plans with an expiration date after December 20, 2025, you can contact Amazon Web Services China Support [2] to request cancellation and pro-rated refunds.

* Can I purchase new RIs and SPs?
Starting March 1, 2025, you will no longer be able to purchase new Standard Reserved Instances or Instance Savings Plans for P3 instances. New Convertible Reserved Instances and Compute Savings Plans will not be applicable for P3 instances starting March 1, 2025.

[1] https://www.amazonaws.cn/en/ec2/instance-types/#Linux_Accelerated_Computing
[2] https://console.amazonaws.cn/support/
'

翻译示例:
'
我们于2017年10月首次推出了Amazon Elastic Compute Cloud (EC2) P3 实例（以下简称“P3实例”），并持续为客户提供可在P3实例上实现的可扩展体验。然而很遗憾，我们将无法继续提供您所期望的P3实例家族的高度弹性服务，因此我们正在和客户一起合作，计划于2025年12月20日前将其工作负载迁移到较新的一代的Amazon EC2 G或P实例。要了解这些实例的更多信息，请参阅我们的文档 [1]。

现有P3实例的用户在截至2025年12月20日退役日期之前不会有任何体验上的变化。然而，从2025年3月1日起，客户将不再能够为P3实例购买新的预留实例或实例节省计划。未来，只有在过去12个月内运行过P3实例的现有P3客户才能重新启动或启动新的P3实例。如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [2] 或阅读我们的常见问题解答。

常见问题解答

* 为什么不再支持P3实例？
Amazon EC2为客户提供可扩展和可靠的服务体验。P3实例不再受支持是因为我们无法继续提供您对P3实例系列所期望的高弹性。

* 我需要做什么？
请在2025年12月20日前将您的工作负载迁移到较新的一代Amazon EC2 G或P实例上。较新的实例预计会提供更好的价格性能。

* 未来谁可以访问P3实例？
只有在过去12个月内运行过P3实例的现有P3客户才能重新启动或启动新的P3实例。

* 我的现有预留实例（RI）和实例节省计划（SP）会发生什么？
您可以在2025年12月20日前通过Amazon EC2控制台或命令行工具将现有的P3可转换预留实例与其他任何Amazon EC2实例的可转换预留实例进行互换。对于P3 可转换预留实例，我们建议将其与较新的一代Amazon EC2 G或P实例进行互换。对于到期日期在2025年12月20日之后的P3标准预留实例或P3实例节省计划，您可以联系亚马逊云科技中国支持团队 [2] 申请取消和按比例退款。

* 我还能购买新的RI和SP吗？
从2025年3月1日起，您将无法再为P3实例购买新的标准预留实例或实例节省计划。自2025年3月1日起，新的可转换预留实例和计算节省计划将不适用于P3实例。

[1] https://www.amazonaws.cn/ec2/instance-types/#Linux_Accelerated_Computing
[2] https://console.amazonaws.cn/support/
'


样例25:
英文原文:
'
We are reaching out regarding a change we are making to the AWS Deep Learning AMI (DLAMIs) products, which requires your action. AWS Deep Learning AMIs provide customized machine images that you can use for deep learning in the cloud. Beginning October 7, 2024, we are deprecating PyTorch 2.0.1 DLAMIs and Multi-framework Deep Learning AMIs (Amazon Linux 2) released between June 1, 2023 and August 31, 2023.

We identified your AWS account uses one of these affected DLAMIs. To mitigate this change, you must update to the latest PyTorch 2.0.1 DLAMI or Multi-framework Deep Learning AMI before November 7, 2024. After this date, these DLAMIs will be removed. If you do not take action by this date, new instances that attempt to make use of the removed AMIs will fail to launch.

Please see the "Affected resource" tab in your AWS Health Dashboard for a list of affected DLAMI(s).

To update to the latest AMI, please refer to the AWS documentation for PyTorch 2.0.1 DLAMIs for Amazon Linux 2 [1] or Ubuntu 20.04 [2], and Multi-framework Deep Learning AMIs for Amazon Linux 2 [3].

Please reach out to AWS Support [4] for questions.

[1] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-amazon-linux-2/ 
[2] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-ubuntu-20-04/ 
[3] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-amazon-linux-2/ 
[4] https://aws.amazon.com/support
'

替换后的英文:
'
We are reaching out regarding a change we are making to the Amazon Deep Learning AMI (DLAMIs) products, which requires your action. Amazon Deep Learning AMIs provide customized machine images that you can use for deep learning in the cloud. Beginning October 7, 2024, we are deprecating PyTorch 2.0.1 DLAMIs and Multi-framework Deep Learning AMIs (Amazon Linux 2) released between June 1, 2023 and August 31, 2023.

We identified your Amazon account uses one of these affected DLAMIs. To mitigate this change, you must update to the latest PyTorch 2.0.1 DLAMI or Multi-framework Deep Learning AMI before November 7, 2024. After this date, these DLAMIs will be removed. If you do not take action by this date, new instances that attempt to make use of the removed AMIs will fail to launch.

Please see the "Affected resource" tab in your Amazon Health Dashboard for a list of affected DLAMI(s).

To update to the latest AMI, please refer to the Amazon documentation for PyTorch 2.0.1 DLAMIs for Amazon Linux 2 [1] or Ubuntu 20.04 [2], and Multi-framework Deep Learning AMIs for Amazon Linux 2 [3].

Please reach out to Amazon Support [4] for questions.

[1] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-amazon-linux-2/ 
[2] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-ubuntu-20-04/ 
[3] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-amazon-linux-2/ 
[4] https://aws.amazon.com/support
'

翻译示例:
'
我们正在就对 Amazon Deep Learning AMis（DLAMIs）产品进行的一项更改通知您，该更改需要您的操作。Amazon Deep Learning AMis 提供可用于云端深度学习的定制机器映像。从 2024 年 10 月 7 日开始，我们将弃用 2023 年 6 月 1 日至 2023 年 8 月 31 日之间发布的 PyTorch 2.0.1 DLAMIs 和 Multi-framework Deep Learning AMIs（Amazon Linux 2）。

我们发现您的 亚马逊云科技 账户使用了其中一个受影响的 DLAMI。为了缓解此更改，您必须在 2024 年 11 月 7 日之前更新到最新的 PyTorch 2.0.1 DLAMI 或 Multi-framework Deep Learning AMI。在此日期之后，这些 DLAMI 将被删除。如果您在截止日期前没有采取行动，则尝试使用已删除 AMI 的新实例将无法启动。

请查看 Amazon Health Dashboard中的“受影响资源”选项卡，以获取受影响 DLAMI 列表。

要更新到最新 AMI，请参考亚马逊云科技文档：

PyTorch 2.0.1 DLAMIs for Amazon Linux 2 [1]
PyTorch 2.0.1 DLAMIs for Ubuntu 20.04 [2]
Multi-framework Deep Learning AMIs for Amazon Linux 2 [3]
如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队 [4]。


[1] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-amazon-linux-2/ 
[2] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-gpu-pytorch-2-0-ubuntu-20-04/ 
[3] https://aws.amazon.com/releasenotes/aws-deep-learning-ami-amazon-linux-2/ 
[4] https://console.amazonaws.cn/support/
'


样例26:
英文原文:
'
We are contacting you with an important update about the Beta FIPS for Application Load Balancer (ALB) in which you are currently enrolled.

On November 20, 2023, we released FIPS Generally Available for ALBs [1], allowing you to create a FIPS-compliant ALB without the requirement of using the Beta FIPS.
This notification is to inform you that Beta FIPS for ALB is being deprecated by February 28, 2024.

To retain FIPS compliance, we require you to complete the following two actions:

1. Modify your existing system that creates ALB to use the new FIPS-compliant TLS security policies [1] since previously allow-listed FIPS Beta policies such as ELBSecurityPolicy-FS-1-2-Res-2019-08 will no longer offer FIPS compliance for newly created load balancers. This action must be completed by February 28, 2024.

2. For Beta FIPS ALBs created before February 28, 2024, please modify their HTTPS listener to use the new FIPS-compliant TLS security policies [1]. This must be completed by March 29, 2024. After this date, any ALBs using FIPS Beta feature will no longer be FIPS-compliant.

A list of your load balancer(s) that are currently configured with Beta FIPS and need to be changed to use new FIPS-compliant TLS security policies can be found under the "Affected resources" tab of your AWS Health Dashboard.

Here is an example command to change the TLS security policy of your ALB to a FIPS-compliant TLS security policy:
$ aws elbv2 modify-listener --listener-arn <LISTENER-ARN> --ssl-policy ELBSecurityPolicy-TLS13-1-2-FIPS-2023-04 --region <REGION-CODE>

If you require further support, please contact us through AWS Support [2].

[1] https://docs.amazonaws.cn/elasticloadbalancing/latest/application/create-https-listener.html#fips-security-policies
[2] https://aws.amazon.com/support
'

替换后的英文:
'

We are contacting you with an important update about the Beta FIPS for Application Load Balancer (ALB) in which you are currently enrolled.

On November 20, 2023, we released FIPS Generally Available for ALBs [1], allowing you to create a FIPS-compliant ALB without the requirement of using the Beta FIPS.
This notification is to inform you that Beta FIPS for ALB is being deprecated by February 28, 2024.

To retain FIPS compliance, we require you to complete the following two actions:

1. Modify your existing system that creates ALB to use the new FIPS-compliant TLS security policies [1] since previously allow-listed FIPS Beta policies such as ELBSecurityPolicy-FS-1-2-Res-2019-08 will no longer offer FIPS compliance for newly created load balancers. This action must be completed by February 28, 2024.

2. For Beta FIPS ALBs created before February 28, 2024, please modify their HTTPS listener to use the new FIPS-compliant TLS security policies [1]. This must be completed by March 29, 2024. After this date, any ALBs using FIPS Beta feature will no longer be FIPS-compliant.

A list of your load balancer(s) that are currently configured with Beta FIPS and need to be changed to use new FIPS-compliant TLS security policies can be found under the "Affected resources" tab.

Here is an example command to change the TLS security policy of your ALB to a FIPS-compliant TLS security policy:
$ aws elbv2 modify-listener --listener-arn --ssl-policy ELBSecurityPolicy-TLS13-1-2-FIPS-2023-04 --region

If you have any questions or concerns, please contact Amazon Web Services China Support [2].

[1] https://docs.amazonaws.cn/en_us/elasticloadbalancing/latest/application/create-https-listener.html#fips-security-policies
[2] https://console.amazonaws.cn/support
'

翻译示例:
'
我们正在与您联系，向您提供有关您的账户当前注册的 Application Load Balancer (ALB) 测试版 FIPS 的重要更新。

2023 年 11 月 20 日，我们为 ALB 发布了广泛可用的 FIPS 适配策略[1]，此次发布允许您在创建符合 FIPS 合规要求的 ALB 时不再需要使用测试版（Beta）的 FIPS 兼容策略。

此通知旨在告知您，ALB 的测试版 FIPS 兼容策略将于 2024 年 2 月 28 日弃用。

为了保持 FIPS 合规性，我们需要您完成以下两项操作：

1. 修改您创建 ALB 的已存在系统以使得 ALB 使用新的符合 FIPS 要求的 TLS 安全策略 [1]，这是因为包括 ELBSecurityPolicy-FS-1-2-Res-2019-08 在内被列入允许列表的 FIPS 测试版策略将不再为新创建的负载均衡器提供 FIPS 合规性适配。此操作必须在 2024 年 2 月 28 日之前完成。

2. 对于 2024 年 2 月 28 日之前创建的使用测试版 FIPS 功能的 ALB，请修改其 HTTPS 侦听器以使用新的适配 FIPS 的 TLS 安全策略 [1]。 此操作必须在 2024 年 3 月 29 日之前完成。在此日期之后，任何使用 FIPS 测试版功能的 ALB 将不再符合 FIPS 合规性。

您可以在“受影响的资源”选项卡下找到当前配置了测试版 FIPS 策略并需要修改为新的 FIPS 适配 TLS 策略的负载均衡器列表。

以下是将 ALB 的 TLS 安全策略更改为符合 FIPS 要求的 TLS 安全策略的示例命令：
$ aws elbv2 modify-listener --listener-arn --ssl-policy ELBSecurityPolicy-TLS13-1-2-FIPS-2023-04 --region

如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队[2]。

[1] https://docs.amazonaws.cn/elasticloadbalancing/latest/application/create-https-listener.html#fips-security-policies
[2] https://console.amazonaws.cn/support
'