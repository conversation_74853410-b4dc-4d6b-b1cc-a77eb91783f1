---

### 更新后的 `mass_email_database_schema.sql.txt`

我已经为您更新了文件中的 `-- 7. 初始化数据 (Seed Data)` 部分。以下是替换后的完整文件内容。

**重要说明**:
1.  **脚本设计为可重复执行**: 使用 `TRUNCATE ... RESTART IDENTITY CASCADE` 来清空旧数据，并使用 `ON CONFLICT DO NOTHING` 来避免因重复执行导致错误。
2.  **优先级（Priority）**: 在 `regex_patterns` 表中，优先级（`priority`）的数值越大，表示越优先匹配。我根据“更具体、更长的模式优先”的原则设置了优先级，这与原始JS代码中的排序逻辑一致。
3.  **外键关联**: `regex_patterns` 表中的 `related_service_id` 使用了子查询 `(SELECT id FROM service_names WHERE base_name = '...')` 来动态获取外键ID，确保了数据的关联正确性。

```sql
-- ====================================================================
-- CN Mass Email Translator 数据库架构脚本
-- 基于"逻辑外化"设计哲学的 PostgreSQL 数据库表结构
-- 
-- 版本: 1.1
-- 创建日期: 2025-01-16
-- 更新描述: 增加了完整的、从POC代码转换而来的初始化数据。
-- ====================================================================

-- 检查 PostgreSQL 版本
DO $$
BEGIN
    IF current_setting('server_version_num')::int < 130000 THEN
        RAISE EXCEPTION 'PostgreSQL version 13.0 or higher is required. Current version: %', version();
    END IF;
END $$;

-- 开启必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ====================================================================
-- 1. 自定义枚举类型 (ENUM Types)
-- ====================================================================

-- 定义翻译任务的状态
CREATE TYPE translation_job_status AS ENUM (
    'pending',              -- 等待处理
    'processing_stage1',    -- JS标准化阶段
    'processing_stage2',    -- LLM翻译阶段
    'processing_stage3',    -- 占位符恢复阶段
    'completed',            -- 已完成
    'failed'                -- 失败
);

-- 定义规则来源
CREATE TYPE rule_source AS ENUM (
    'web_scrape',           -- 网页抓取
    'pdf_parse',            -- PDF解析
    'manual'                -- 手动添加
);

-- 定义用户反馈满意度
CREATE TYPE feedback_satisfaction AS ENUM (
    'satisfied',            -- 满意
    'unsatisfied',          -- 不满意
    'neutral'               -- 中性
);

-- 定义正则表达式模式的类型
CREATE TYPE regex_pattern_type AS ENUM (
    'SERVICE_NAME',         -- 服务名称
    'TIMEZONE',             -- 时区
    'CLI_COMMAND',          -- CLI命令
    'URL',                  -- URL
    'GENERAL'               -- 通用
);

-- ====================================================================
-- 2. 触发器函数
-- ====================================================================

-- 自动更新 updated_at 时间戳的触发器函数
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ====================================================================
-- 3. 核心业务逻辑表 (Core Business Logic Tables)
-- ====================================================================

-- ---------------------------------
-- 表: service_names (服务名称) - v2 (支持自动化同步)
-- 描述: 存储所有AWS服务的标准中英文名称、缩写等信息
-- ---------------------------------
-- 先删除旧表（如果存在）
-- DROP TABLE IF EXISTS service_names CASCADE;

CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    
    -- 新增：作为同步流程的业务主键，来自网页的权威全称
    authoritative_full_name VARCHAR(255) NOT NULL UNIQUE,
    
    -- 重新定义的 base_name，用于翻译逻辑分组
    base_name VARCHAR(255) NOT NULL,
    
    -- 新增：来自PDF的 internal name
    internal_name VARCHAR(255),

    full_name_en VARCHAR(255) NOT NULL,            -- 英文全称, 与 authoritative_full_name 相同
    short_name_en VARCHAR(100) NOT NULL,           -- 英文缩写, 来自PDF或等于全称
    
    full_name_cn VARCHAR(255),                     -- 中文全称（预留字段）
    short_name_cn VARCHAR(100),                    -- 中文缩写（预留字段）
    service_code VARCHAR(50),                      -- AWS官方服务代码, 如 "ec2"
    source rule_source NOT NULL DEFAULT 'manual', -- 数据来源
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    notes TEXT,                                    -- 备注
    last_synced_at TIMESTAMPTZ,                    -- 上次同步时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为关键字段添加索引
CREATE INDEX idx_service_names_base_name ON service_names(base_name);

-- 表和列的注释更新
COMMENT ON TABLE service_names IS '存储AWS服务的标准中英文名称和缩写信息，支持多数据源同步';
COMMENT ON COLUMN service_names.authoritative_full_name IS '来自官网网页的权威服务全称，作为同步操作的唯一业务键';
COMMENT ON COLUMN service_names.base_name IS '用于翻译逻辑的规范化基础名称（不含括号），根据 authoritative_full_name 推导';
COMMENT ON COLUMN service_names.internal_name IS '来自PDF文档的 "AWS offering (internal name)"';
COMMENT ON COLUMN service_names.full_name_en IS '首次使用时的英文全称，与 authoritative_full_name 保持一致';
COMMENT ON COLUMN service_names.short_name_en IS '后续使用的英文简称，主要来自PDF';

-- 别忘了为新表重新应用触发器
CREATE TRIGGER set_timestamp_service_names
    BEFORE UPDATE ON service_names
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- ---------------------------------
-- 表: brand_term_mappings (品牌术语映射)
-- 描述: 存储需要强制替换的品牌术语和通用词汇
-- 用途: 将品牌词替换逻辑从代码中外部化
-- ---------------------------------
CREATE TABLE brand_term_mappings (
    id BIGSERIAL PRIMARY KEY,
    term_en VARCHAR(255) NOT NULL UNIQUE,          -- 英文术语, 如 "Amazon Web Services Support"
    term_cn VARCHAR(255) NOT NULL,                 -- 中文替换, 如 "亚马逊云科技中国支持团队"
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    notes TEXT,                                    -- 备注
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 品牌术语映射表注释
COMMENT ON TABLE brand_term_mappings IS '存储品牌术语的强制替换规则，支持动态启用/禁用';
COMMENT ON COLUMN brand_term_mappings.term_en IS '需要被替换的英文术语';
COMMENT ON COLUMN brand_term_mappings.term_cn IS '对应的中文替换内容';

-- ---------------------------------
-- 表: url_mappings (URL 映射规则)
-- 描述: 存储URL本地化的替换规则
-- 用途: 配置化URL本地化逻辑，如 aws.amazon.com -> amazonaws.cn
-- ---------------------------------
CREATE TABLE url_mappings (
    id BIGSERIAL PRIMARY KEY,
    source_pattern TEXT NOT NULL,                  -- 源URL匹配模式
    target_pattern TEXT NOT NULL,                  -- 目标URL替换模式
    is_regex BOOLEAN NOT NULL DEFAULT FALSE,       -- source_pattern是否为正则表达式
    priority INT NOT NULL DEFAULT 0,               -- 应用优先级，数字越大优先级越高
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    notes TEXT,                                    -- 备注
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(source_pattern, is_regex)
);

-- URL映射规则表注释
COMMENT ON TABLE url_mappings IS '存储URL本地化的替换规则，支持正则表达式和优先级控制';
COMMENT ON COLUMN url_mappings.is_regex IS '标识source_pattern是否为正则表达式';
COMMENT ON COLUMN url_mappings.priority IS '应用优先级，数字越大优先级越高，用于处理重叠规则';

-- ---------------------------------
-- 表: regex_patterns (正则表达式模式)
-- 描述: 存储用于识别服务名称、时间等实体的正则表达式
-- 用途: 完全替代硬编码的 servicePatterns 数组
-- ---------------------------------
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL UNIQUE,     -- 模式的易读名称, e.g., "EC2_INSTANCE_WITH_SUFFIX"
    pattern_type VARCHAR(50) NOT NULL,             -- 模式类型 (改为VARCHAR以提供更大灵活性)
    regex_string TEXT NOT NULL,                    -- 正则表达式本身
    related_service_id BIGINT,                     -- 关联的服务ID (外键)
    service_code VARCHAR(50),                      -- 服务代码，如 "ec2", "s3"
    priority INTEGER NOT NULL DEFAULT 100,         -- 匹配优先级，数字越大优先级越高 (调整默认值)
    notes TEXT,                                    -- 可以描述捕获组的含义等
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    validation_status VARCHAR(20) DEFAULT 'pending', -- 验证状态: pending, valid, invalid
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_related_service
        FOREIGN KEY(related_service_id)
        REFERENCES service_names(id)
        ON DELETE SET NULL
);

-- 正则表达式模式表注释
COMMENT ON TABLE regex_patterns IS '存储用于实体识别的正则表达式，支持分类和优先级管理';
COMMENT ON COLUMN regex_patterns.pattern_type IS '模式类型：SERVICE_NAME, TIMEZONE, CLI_COMMAND, URL, GENERAL';
COMMENT ON COLUMN regex_patterns.related_service_id IS '关联的服务ID，将正则表达式与service_names记录关联';
COMMENT ON COLUMN regex_patterns.priority IS '匹配优先级，控制多个正则匹配同一文本时的应用顺序';

-- ====================================================================
-- 4. 核心工作流与数据表 (Core Workflow & Data Tables)
-- ====================================================================

-- ---------------------------------
-- 表: translation_jobs (翻译任务)
-- 描述: 记录每一次翻译请求的完整生命周期
-- 用途: 系统中枢，提供完整的可追溯性和4阶段处理流程追踪
-- ---------------------------------
CREATE TABLE translation_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    status translation_job_status NOT NULL DEFAULT 'pending', -- 当前任务状态
    original_text TEXT NOT NULL,                           -- 用户提交的原始文本
    stage1_standardized_en TEXT,                           -- JS标准化后的英文文本
    stage2_llm_input TEXT,                                 -- 发送给LLM的带占位符的文本
    stage2_llm_output TEXT,                                -- LLM返回的带占位符的译文
    stage3_final_cn TEXT,                                  -- 最终恢复占位符后的中文文本
    placeholder_map JSONB,                                 -- 占位符与原文的映射, e.g., {"__SRVCNM_0__": "Amazon EC2"}
    service_mention_state JSONB,                           -- 服务首次/后续提及状态, e.g., {"Amazon EC2": {"mentioned": true, "usedFullName": true}}
    error_message TEXT,                                    -- 如果失败，记录错误信息
    error_stage VARCHAR(50),                               -- 失败发生的阶段
    processing_time_ms INT,                                -- 总处理耗时（毫秒）
    submitted_by VARCHAR(255),                             -- 提交者标识
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- 翻译任务表注释
COMMENT ON TABLE translation_jobs IS '记录翻译任务的完整生命周期，支持4阶段处理流程追踪';
COMMENT ON COLUMN translation_jobs.status IS '任务状态：pending, processing_stage1-3, completed, failed';
COMMENT ON COLUMN translation_jobs.placeholder_map IS '占位符与原文的映射，JSON格式存储';
COMMENT ON COLUMN translation_jobs.service_mention_state IS '服务首次/后续提及状态，JSON格式存储';
COMMENT ON COLUMN translation_jobs.error_stage IS '失败发生的阶段，便于精确定位问题';

-- ---------------------------------
-- 表: feedback_submissions (用户反馈)
-- 描述: 存储通过Tampermonkey脚本收集的用户反馈
-- 用途: 与S3存储集成，支持反馈数据的完整性和可追溯性
-- ---------------------------------
CREATE TABLE feedback_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    translation_job_id UUID,                               -- 关联的翻译任务ID (可能为空)
    page_url TEXT NOT NULL,                                 -- 反馈提交页面URL
    satisfaction feedback_satisfaction NOT NULL,            -- 满意度
    comments TEXT,                                          -- 用户评论
    user_agent TEXT,                                        -- 用户的User Agent
    s3_object_key VARCHAR(1024) NOT NULL UNIQUE,            -- 在S3中存储的原始JSON文件Key
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_translation_job
        FOREIGN KEY(translation_job_id)
        REFERENCES translation_jobs(id)
        ON DELETE SET NULL
);

-- 用户反馈表注释
COMMENT ON TABLE feedback_submissions IS '存储用户反馈数据，与S3存储集成';
COMMENT ON COLUMN feedback_submissions.translation_job_id IS '关联的翻译任务ID，允许为空支持通用反馈';
COMMENT ON COLUMN feedback_submissions.satisfaction IS '满意度：satisfied, unsatisfied, neutral';
COMMENT ON COLUMN feedback_submissions.s3_object_key IS 'S3中原始JSON文件的位置，确保数据完整性';

-- ====================================================================
-- 5. 索引创建 (Index Creation)
-- ====================================================================

-- 服务名称表索引
CREATE INDEX idx_service_names_service_code ON service_names(service_code);
CREATE INDEX idx_service_names_is_active ON service_names(is_active);
CREATE INDEX idx_service_names_source ON service_names(source);
CREATE INDEX idx_service_names_last_synced ON service_names(last_synced_at);

-- 品牌术语映射表索引
CREATE INDEX idx_brand_term_mappings_is_active ON brand_term_mappings(is_active);

-- URL映射表索引
CREATE INDEX idx_url_mappings_priority ON url_mappings(priority DESC);
CREATE INDEX idx_url_mappings_is_active ON url_mappings(is_active);
CREATE INDEX idx_url_mappings_is_regex ON url_mappings(is_regex);

-- 正则表达式模式表索引 (优化后的索引结构)
CREATE INDEX idx_regex_patterns_priority ON regex_patterns(priority DESC, id ASC);
CREATE INDEX idx_regex_patterns_service ON regex_patterns(related_service_id);
CREATE INDEX idx_regex_patterns_type_active ON regex_patterns(pattern_type, is_active);
CREATE INDEX idx_regex_patterns_service_code ON regex_patterns(service_code);
CREATE INDEX idx_regex_patterns_validation_status ON regex_patterns(validation_status);

-- 翻译任务表索引
CREATE INDEX idx_translation_jobs_status ON translation_jobs(status);
CREATE INDEX idx_translation_jobs_submitted_by ON translation_jobs(submitted_by);
CREATE INDEX idx_translation_jobs_submitted_at ON translation_jobs(submitted_at);
CREATE INDEX idx_translation_jobs_completed_at ON translation_jobs(completed_at);
-- GIN索引用于高效查询JSONB数据
CREATE INDEX idx_translation_jobs_placeholder_map ON translation_jobs USING GIN (placeholder_map);
CREATE INDEX idx_translation_jobs_service_mention_state ON translation_jobs USING GIN (service_mention_state);

-- 用户反馈表索引
CREATE INDEX idx_feedback_submissions_satisfaction ON feedback_submissions(satisfaction);
CREATE INDEX idx_feedback_submissions_submitted_at ON feedback_submissions(submitted_at);
CREATE INDEX idx_feedback_submissions_translation_job ON feedback_submissions(translation_job_id);

-- ====================================================================
-- 6. 触发器创建 (Trigger Creation)
-- ====================================================================

-- 为需要的表添加自动更新时间戳触发器
CREATE TRIGGER set_timestamp_service_names
    BEFORE UPDATE ON service_names
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_brand_term_mappings
    BEFORE UPDATE ON brand_term_mappings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_url_mappings
    BEFORE UPDATE ON url_mappings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_regex_patterns
    BEFORE UPDATE ON regex_patterns
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- ====================================================================
-- 7. 初始化数据 (Seed Data)
-- ====================================================================
-- 清空现有数据以确保脚本可重复执行
TRUNCATE TABLE brand_term_mappings, url_mappings, regex_patterns, service_names RESTART IDENTITY CASCADE;

-- ---------------------------------
-- A. 品牌术语映射 (brand_term_mappings)
-- ---------------------------------
INSERT INTO brand_term_mappings (term_en, term_cn, notes) VALUES
('Amazon Web Services Support', '亚马逊云科技中国支持团队', '标准替换'),
('AWS Accounts Teams', 'Amazon Web Services Support', '标准化为官方支持'),
('AWS Account', 'Amazon Web Services Account', '标准化为官方账户'),
('AWS Support Center', 'Amazon Web Services Support', '标准化为官方支持'),
('AWS Support Team', 'Amazon Web Services Support', '标准化为官方支持'),
('AWS Support', 'Amazon Web Services Support', '标准化为官方支持'),
('AWS Management Console', 'Amazon Web Services Console', '标准化为官方控制台'),
('AWS console', 'Amazon Web Services Console', '标准化为官方控制台'),
('AWS SDK', 'Amazon SDK', '品牌标准化'),
('the AWS documentation', 'the Amazon Web Services documentation', '品牌标准化'),
('AWS documentation', 'Amazon Web Services documentation', '品牌标准化'),
('AWS docs', 'Amazon Web Services documentation', '品牌标准化'),
('AWS Service Quotas console', 'Service Quotas console', '品牌标准化'),
('AWS Service Quotas', 'Service Quotas', '品牌标准化'),
('AWS Nitro System', 'Amazon Nitro System', '品牌标准化'),
('AWS Nitro', 'Amazon Nitro', '品牌标准化'),
('Amazon', '亚马逊云科技', '独立品牌词，应用层需处理上下文排除逻辑'),
('AWS Web Services', 'Amazon Web Services', '常见拼写错误纠正')
ON CONFLICT (term_en) DO NOTHING;

-- ---------------------------------
-- B. URL 映射规则 (url_mappings)
-- ---------------------------------
INSERT INTO url_mappings (source_pattern, target_pattern, priority, is_regex, notes) VALUES
('^https?:\/\/amazonaws\.cn\/support\/?$', 'https://console.amazonaws.cn/support/', 200, TRUE, '修正错误的 .cn/support 链接'),
('^https?:\/\/aws\.amazon\.com\/(support|contact-us)\/?$', 'https://console.amazonaws.cn/support/', 200, TRUE, '将旧的 support/contact-us 链接统一到新的 .cn 控制台'),
('^https?:\/\/aws\.amazon\.com\/ec2\/instance-types\/#Accelerated_Computing$', 'https://www.amazonaws.cn/ec2/instance-types/#Linux_Accelerated_Computing', 200, TRUE, '特定文档页面本地化'),
('^https?:\/\/docs\.aws\.amazon\.com', 'https://docs.amazonaws.cn', 150, TRUE, '文档域名本地化'),
('^https?:\/\/([a-z0-9-]+\.)?console\.aws\.amazon\.com', 'https://\1console.amazonaws.com.cn', 150, TRUE, '控制台域名本地化，保留子域名'),
('^https?:\/\/([^./]+)\.s3\.amazonaws\.com(?!\.cn)', 'https://\1.s3.cn-north-1.amazonaws.com.cn', 120, TRUE, 'S3桶URL区域化到cn-north-1'),
('^https?:\/\/([^./]+)\.([^./]+)\.aws\.amazon\.com(?!\.cn)', 'https://\1.\2.amazonaws.com.cn', 110, TRUE, '通用服务终端节点本地化'),
('^https?:\/\/aws\.amazon\.com', 'https://www.amazonaws.cn', 100, TRUE, '主域名本地化')
ON CONFLICT (source_pattern, is_regex) DO NOTHING;

-- ---------------------------------
-- C. AWS 服务权威名称 (service_names) - v2 更新版
-- ---------------------------------
-- 注意：此 INSERT 语句已适配 v2 表结构
INSERT INTO service_names 
    (authoritative_full_name, base_name, internal_name, full_name_en, short_name_en, service_code) 
VALUES
    ('Amazon Health Dashboard', 'Amazon Health Dashboard', NULL, 'Amazon Health Dashboard', 'Amazon Health', 'health'),
    ('Amazon Elastic Compute Cloud (EC2)', 'Amazon Elastic Compute Cloud', NULL, 'Amazon Elastic Compute Cloud (EC2)', 'Amazon EC2', 'ec2'),
    ('Amazon EC2 Auto Scaling', 'Amazon EC2 Auto Scaling', NULL, 'Amazon EC2 Auto Scaling', 'Amazon EC2 Auto Scaling', 'autoscaling'),
    ('Amazon Elastic Block Store (EBS)', 'Amazon Elastic Block Store', NULL, 'Amazon Elastic Block Store (EBS)', 'Amazon EBS', 'ebs'),
    ('Amazon Identity and Access Management (IAM)', 'Amazon Identity and Access Management', NULL, 'Amazon Identity and Access Management (IAM)', 'IAM', 'iam'),
    ('Amazon IAM Identity Center', 'Amazon IAM Identity Center', NULL, 'Amazon IAM Identity Center', 'IAM Identity Center', 'sso'),
    ('Amazon Virtual Private Cloud (VPC)', 'Amazon Virtual Private Cloud', NULL, 'Amazon Virtual Private Cloud (VPC)', 'Amazon VPC', 'vpc'),
    ('Amazon Relational Database Service (RDS)', 'Amazon Relational Database Service', NULL, 'Amazon Relational Database Service (RDS)', 'Amazon RDS', 'rds'),
    ('Amazon Aurora', 'Amazon Aurora', NULL, 'Amazon Aurora', 'Aurora', 'rds'),
    ('Amazon Security Token Service (Amazon STS)', 'Amazon Security Token Service', NULL, 'Amazon Security Token Service (Amazon STS)', 'Amazon STS', 'sts'),
    ('Amazon Elastic Beanstalk', 'Amazon Elastic Beanstalk', NULL, 'Amazon Elastic Beanstalk', 'Elastic Beanstalk', 'elasticbeanstalk'),
    ('Amazon Elastic Kubernetes Service (EKS)', 'Amazon Elastic Kubernetes Service', NULL, 'Amazon Elastic Kubernetes Service (EKS)', 'Amazon EKS', 'eks'),
    ('Elastic Load Balancing (ELB)', 'Elastic Load Balancing', NULL, 'Elastic Load Balancing (ELB)', 'ELB', 'elb'),
    ('Amazon Linux 2023', 'Amazon Linux 2023', NULL, 'Amazon Linux 2023', 'Amazon Linux 2023', 'amazon-linux'),
    ('Amazon Machine Image (AMI)', 'Amazon Machine Image', NULL, 'Amazon Machine Image (AMI)', 'AMI', 'ec2'),
    ('Amazon CloudFormation', 'Amazon CloudFormation', NULL, 'Amazon CloudFormation', 'CloudFormation', 'cloudformation'),
    ('Amazon Glue', 'Amazon Glue', NULL, 'Amazon Glue', 'Glue', 'glue'),
    ('Amazon Lambda', 'Amazon Lambda', NULL, 'Amazon Lambda', 'Lambda', 'lambda'),
    ('Amazon Serverless Application Model (Amazon SAM)', 'Amazon Serverless Application Model', NULL, 'Amazon Serverless Application Model (Amazon SAM)', 'Amazon SAM', 'sam'),
    ('Amazon Simple Storage Service (S3)', 'Amazon Simple Storage Service', NULL, 'Amazon Simple Storage Service (S3)', 'Amazon S3', 's3'),
    ('Amazon Simple Storage Service Glacier (Amazon S3 Glacier)', 'Amazon Simple Storage Service Glacier', NULL, 'Amazon Simple Storage Service Glacier (Amazon S3 Glacier)', 'Amazon S3 Glacier', 'glacier'),
    ('Amazon DynamoDB', 'Amazon DynamoDB', NULL, 'Amazon DynamoDB', 'DynamoDB', 'dynamodb'),
    ('Amazon CloudWatch', 'Amazon CloudWatch', NULL, 'Amazon CloudWatch', 'CloudWatch', 'cloudwatch'),
    ('Amazon CloudWatch Events', 'Amazon CloudWatch Events', NULL, 'Amazon CloudWatch Events', 'CloudWatch Events', 'events'),
    ('Amazon CloudWatch Logs', 'Amazon CloudWatch Logs', NULL, 'Amazon CloudWatch Logs', 'CloudWatch Logs', 'logs'),
    ('Amazon Simple Notification Service (SNS)', 'Amazon Simple Notification Service', NULL, 'Amazon Simple Notification Service (SNS)', 'Amazon SNS', 'sns'),
    ('Amazon Simple Queue Service (SQS)', 'Amazon Simple Queue Service', NULL, 'Amazon Simple Queue Service (SQS)', 'Amazon SQS', 'sqs'),
    ('Amazon Systems Manager', 'Amazon Systems Manager', NULL, 'Amazon Systems Manager', 'Systems Manager', 'ssm'),
    ('Amazon Batch', 'Amazon Batch', NULL, 'Amazon Batch', 'Amazon Batch', 'batch'),
    ('Amazon Command Line Interface (Amazon CLI)', 'Amazon Command Line Interface', NULL, 'Amazon Command Line Interface (Amazon CLI)', 'Amazon CLI', 'cli'),
    ('Service Control Policies (SCPs)', 'Service Control Policies', NULL, 'Service Control Policies (SCPs)', 'SCPs', 'organizations')
ON CONFLICT (authoritative_full_name) DO NOTHING;

-- ---------------------------------
-- D. 服务名称识别的正则表达式 (regex_patterns)
-- ---------------------------------
-- 注：此脚本应在 service_names 表数据插入后执行。
-- 使用 DO 块和变量来确保外键关联的健壮性。

DO $$
DECLARE
    service_id_var BIGINT;
BEGIN
    -- === AWS Health (Highest Priority) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Health Dashboard');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, service_code, priority, validation_status) VALUES
    ('HEALTH_DASHBOARD_FULL_1', 'SERVICE_NAME', 'Amazon\\s+Health\\s+Dashboard', service_id_var, 'health', 125, 'valid'),
    ('HEALTH_DASHBOARD_FULL_2', 'SERVICE_NAME', 'AWS\\s+Health\\s+Dashboard', service_id_var, 'health', 125, 'valid'),
    ('HEALTH_DASHBOARD_SHORT_1', 'SERVICE_NAME', 'Amazon\\s+Health\\b(?!Lake|Imaging)', service_id_var, 'health', 120, 'valid'),
    ('HEALTH_DASHBOARD_SHORT_2', 'SERVICE_NAME', 'AWS\\s+Health\\b(?!Lake|Imaging|\\s+Dashboard)', service_id_var, 'health', 120, 'valid')
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Elastic Compute Cloud (EC2) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Elastic Compute Cloud');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, service_code, priority, validation_status, notes) VALUES
    ('EC2_FULL_COMPLEX_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))', service_id_var, 'ec2', 120, 'valid', 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EC2_FULL_STANDARD', 'SERVICE_NAME', '(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)(?<!https?://[^\\s]*)\\bAmazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\b(?![:_-])(?![^\\s]*\\.[a-z]{2,4})', service_id_var, 'ec2', 115, 'valid', 'Standard full name pattern with boundary protection'),
    ('EC2_SHORT_COMPLEX_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+EC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))', service_id_var, 'ec2', 110, 'valid', 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EC2_SHORT_STANDARD', 'SERVICE_NAME', '(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)(?<!https?://[^\\s]*)\\bAmazon\\s+EC2\\b(?![:_-])(?![^\\s]*\\.[a-z]{2,4})', service_id_var, 'ec2', 105, 'valid', 'Standard short name pattern with boundary protection'),
    ('EC2_ACRONYM_COMPLEX_SUFFIX', 'SERVICE_NAME', '(?<![:_-])\\bEC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))(?![:_-])', service_id_var, 'ec2', 100, 'valid', 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EC2_ACRONYM_STANDARD', 'SERVICE_NAME', '(?<![:_-])\\bEC2\\b(?![:_-])', service_id_var, 'ec2', 95, 'valid', 'Pure acronym pattern with boundary protection')
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon EC2 Auto Scaling ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon EC2 Auto Scaling');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('EC2_AUTOSCALING', 'SERVICE_NAME', 'Amazon\\s+EC2\\s+Auto\\s+Scaling', service_id_var, 100)
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Elastic Block Store (EBS) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Elastic Block Store');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority, notes) VALUES
    ('EBS_FULL_WITH_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Block\\s+Store\\s*\\(EBS\\)\\s*(volume\\(s\\)?|volumes|volume)', service_id_var, 110, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EBS_FULL', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Block\\s+Store\\s*\\(EBS\\)', service_id_var, 105, ''),
    ('EBS_SHORT_WITH_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+EBS\\s*(volume\\(s\\)?|volumes|volume)', service_id_var, 100, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EBS_SHORT', 'SERVICE_NAME', 'Amazon\\s+EBS\\b', service_id_var, 95, ''),
    ('EBS_ACRONYM_WITH_SUFFIX', 'SERVICE_NAME', '\\bEBS\\s*(volume\\(s\\)?|volumes|volume)', service_id_var, 90, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('EBS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bEBS\\b', service_id_var, 85, '')
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Identity and Access Management (IAM) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Identity and Access Management');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority, notes) VALUES
    ('IAM_FULL_NO_BRAND', 'SERVICE_NAME', '(?<!Amazon\\s|AWS\\s)Identity\\s+and\\s+Access\\s+Management\\s*\\(IAM\\)', service_id_var, 120, ''),
    ('IAM_AWS_WITH_SUFFIX', 'SERVICE_NAME', '\\bAWS\\s+IAM\\s+(policy|policies|role|roles|user|users)', service_id_var, 115, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('IAM_FULL_WITH_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+Identity\\s+and\\s+Access\\s+Management\\s*\\(IAM\\)\\s*(policy|policies|role|roles|user|users)', service_id_var, 110, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('IAM_FULL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Identity\\s+and\\s+Access\\s+Management\\s*\\(IAM\\)', service_id_var, 105, ''),
    ('IAM_FULL_AWS', 'SERVICE_NAME', 'AWS\\s+Identity\\s+and\\s+Access\\s+Management\\s*\\(IAM\\)', service_id_var, 105, ''),
    ('IAM_FULL_AWS_NO_ACRONYM', 'SERVICE_NAME', 'AWS\\s+Identity\\s+and\\s+Access\\s+Management\\b(?!.*Analyzer)', service_id_var, 100, ''),
    ('IAM_ACRONYM_WITH_SUFFIX', 'SERVICE_NAME', '\\bIAM\\s+(policy|policies|role|roles|user|users)', service_id_var, 95, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('IAM_ACRONYM_AWS', 'SERVICE_NAME', '\\bAWS\\s+IAM\\b(?!.*Access\\s+Analyzer|\\s+(?:policy|policies|role|roles|user|users))', service_id_var, 90, ''),
    ('IAM_ACRONYM_ONLY', 'SERVICE_NAME', '\\bIAM\\b(?!.*Access\\s+Analyzer|\\s+Identity\\s+Center|\\s+(?:policy|policies|role|roles|user|users))', service_id_var, 85, '')
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon IAM Identity Center ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon IAM Identity Center');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('IAM_IDENTITY_CENTER', 'SERVICE_NAME', 'Amazon\\s+IAM\\s+Identity\\s+Center', service_id_var, 125)
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Virtual Private Cloud (VPC) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Virtual Private Cloud');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('VPC_FULL', 'SERVICE_NAME', 'Amazon\\s+Virtual\\s+Private\\s+Cloud\\s*\\(VPC\\)(s)?', service_id_var, 100),
    ('VPC_SHORT', 'SERVICE_NAME', 'Amazon\\s+VPC(s)?\\b', service_id_var, 95),
    ('VPC_ACRONYM_ONLY', 'SERVICE_NAME', '\\bVPC(s)?\\b', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Relational Database Service (RDS) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Relational Database Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority, notes) VALUES
    ('RDS_FULL_WITH_FLAVOR', 'SERVICE_NAME', 'Amazon\\s+Relational\\s+Database\\s+Service\\s*\\(RDS\\)\\s*(for\\s+[\\w\\s().-]+)', service_id_var, 110, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('RDS_SHORT_WITH_FLAVOR', 'SERVICE_NAME', 'Amazon\\s+RDS\\s*(for\\s+[\\w\\s().-]+)', service_id_var, 105, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('RDS_ACRONYM_WITH_FLAVOR_1', 'SERVICE_NAME', '(?<![:_-])\\bRDS\\s*(for\\s+[\\w\\s().-]+)', service_id_var, 100, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('RDS_ACRONYM_WITH_FLAVOR_2', 'SERVICE_NAME', 'AWS\\s+RDS\\s*(for\\s+[\\w\\s().-]+)', service_id_var, 100, 'isCompoundWithSuffix: true, suffixGroup: 1'),
    ('RDS_FULL_AMAZON_ALIAS', 'SERVICE_NAME', 'Amazon\\s+Relational\\s+Database\\s+Service\\s*\\(Amazon\\s+RDS\\)', service_id_var, 95, ''),
    ('RDS_FULL_AWS_ALIAS', 'SERVICE_NAME', 'AWS\\s+Relational\\s+Database\\s+Service\\s*\\(RDS\\)', service_id_var, 90, ''),
    ('RDS_FULL', 'SERVICE_NAME', 'Amazon\\s+Relational\\s+Database\\s+Service\\s*\\(RDS\\)', service_id_var, 90, ''),
    ('RDS_SHORT', 'SERVICE_NAME', 'Amazon\\s+RDS\\b(?!(:[\\w-]+|\\s*Proxy|\\s*Optimized\\s+Read|\\s*Optimized\\s+Write))', service_id_var, 85, ''),
    ('RDS_ACRONYM_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bRDS\\b(?!(:[\\w-]+|\\s*Proxy|\\s*Optimized\\s+Read|\\s*Optimized\\s+Write|\\s*for\\s+))', service_id_var, 80, '')
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Aurora ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Aurora');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('AURORA_POSTGRESQL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Aurora\\s+PostgreSQL\\b', service_id_var, 110),
    ('AURORA_POSTGRESQL_AWS', 'SERVICE_NAME', 'AWS\\s+Aurora\\s+PostgreSQL\\b', service_id_var, 110),
    ('AURORA_POSTGRESQL_ONLY', 'SERVICE_NAME', '\\bAurora\\s+PostgreSQL\\b', service_id_var, 105),
    ('AURORA_MYSQL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Aurora\\s+MySQL\\b', service_id_var, 110),
    ('AURORA_MYSQL_AWS', 'SERVICE_NAME', 'AWS\\s+Aurora\\s+MySQL\\b', service_id_var, 110),
    ('AURORA_MYSQL_ONLY', 'SERVICE_NAME', '\\bAurora\\s+MySQL\\b', service_id_var, 105),
    ('AURORA_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Aurora\\b', service_id_var, 100),
    ('AURORA_AWS', 'SERVICE_NAME', 'AWS\\s+Aurora\\b', service_id_var, 100),
    ('AURORA_ONLY', 'SERVICE_NAME', '\\bAurora\\b(?![\s-](?:PostgreSQL|MySQL)\\b)', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Amazon Security Token Service (STS) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Security Token Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('STS_FULL_AMAZON_ALIAS', 'SERVICE_NAME', 'Amazon\\s+Security\\s+Token\\s+Service\\s*\\(Amazon\\s+STS\\)', service_id_var, 110),
    ('STS_FULL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Security\\s+Token\\s+Service\\s*\\(STS\\)', service_id_var, 105),
    ('STS_FULL_AWS', 'SERVICE_NAME', 'AWS\\s+Security\\s+Token\\s+Service\\s*\\(STS\\)', service_id_var, 105),
    ('STS_FULL_AMAZON_NO_ACRONYM', 'SERVICE_NAME', 'Amazon\\s+Security\\s+Token\\s+Service\\b(?!.*\\s*\\(STS\\))', service_id_var, 100),
    ('STS_FULL_AWS_NO_ACRONYM', 'SERVICE_NAME', 'AWS\\s+Security\\s+Token\\s+Service\\b(?!.*\\s*\\(STS\\))', service_id_var, 100),
    ('STS_SHORT_AMAZON', 'SERVICE_NAME', 'Amazon\\s+STS\\b', service_id_var, 95),
    ('STS_SHORT_AWS', 'SERVICE_NAME', 'AWS\\s+STS\\b', service_id_var, 95),
    ('STS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSTS\\b(?![:-])', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    -- === Other Services (Simplified for brevity) ===
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Elastic Beanstalk');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('ELASTICBEANSTALK_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Beanstalk', service_id_var, 100),
    ('ELASTICBEANSTALK_AWS', 'SERVICE_NAME', 'AWS\\s+Elastic\\s+Beanstalk', service_id_var, 100),
    ('ELASTICBEANSTALK_ONLY', 'SERVICE_NAME', '\\bElastic\\s+Beanstalk\\b', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Elastic Kubernetes Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('EKS_FULL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Kubernetes\\s+Service\\s*\\(EKS\\)', service_id_var, 100),
    ('EKS_FULL_AWS', 'SERVICE_NAME', 'AWS\\s+Elastic\\s+Kubernetes\\s+Service\\s*\\(EKS\\)', service_id_var, 100),
    ('EKS_SHORT_AMAZON', 'SERVICE_NAME', 'Amazon\\s+EKS\\b', service_id_var, 95),
    ('EKS_SHORT_AWS', 'SERVICE_NAME', 'AWS\\s+EKS\\b', service_id_var, 95),
    ('EKS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bEKS\\b(?![:\\-])', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Elastic Load Balancing');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('ELB_FULL', 'SERVICE_NAME', 'Elastic\\s+Load\\s+Balancing\\s*\\(ELB\\)', service_id_var, 100),
    ('ELB_NO_ACRONYM', 'SERVICE_NAME', '\\bElastic\\s+Load\\s+Balancing\\b(?!\\s*\\(ELB\\))', service_id_var, 95),
    ('ELB_ACRONYM_ONLY', 'SERVICE_NAME', '\\bELB\\b(?![:\\-])', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Linux 2023');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('AMAZONLINUX_2023_FULL', 'SERVICE_NAME', 'Amazon\\s+Linux\\s+2023', service_id_var, 100),
    ('AMAZONLINUX_2023_ACRONYM', 'SERVICE_NAME', 'AL2023', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Machine Image');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('AMI_FULL', 'SERVICE_NAME', 'Amazon\\s+Machine\\s+Image(?:s)?\\s*\\(AMI(?:s)?\\)', service_id_var, 100),
    ('AMI_NO_ACRONYM', 'SERVICE_NAME', 'Amazon\\s+Machine\\s+Image(?:s)?\\b', service_id_var, 95),
    ('AMI_ACRONYM_ONLY', 'SERVICE_NAME', '\\bAMI(?:s)?\\b', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon CloudFormation');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('CLOUDFORMATION_AMAZON', 'SERVICE_NAME', 'Amazon\\s+CloudFormation\\b', service_id_var, 100),
    ('CLOUDFORMATION_ONLY', 'SERVICE_NAME', '\\bCloudFormation\\b', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Glue');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('GLUE_AWS', 'SERVICE_NAME', 'AWS\\s+Glue\\b', service_id_var, 100),
    ('GLUE_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Glue\\b', service_id_var, 100),
    ('GLUE_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bGlue\\b(?![:_-])', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Lambda');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('LAMBDA_AWS', 'SERVICE_NAME', 'AWS\\s+Lambda', service_id_var, 100),
    ('LAMBDA_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Lambda', service_id_var, 100),
    ('LAMBDA_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bLambda\\b(?![:_-])', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Serverless Application Model');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('SAM_FULL_AMAZON_ALIAS', 'SERVICE_NAME', 'Amazon\\s+Serverless\\s+Application\\s+Model\\s*\\(Amazon\\s+SAM\\)', service_id_var, 110),
    ('SAM_FULL_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Serverless\\s+Application\\s+Model\\s*\\(SAM\\)', service_id_var, 105),
    ('SAM_FULL_AWS', 'SERVICE_NAME', 'AWS\\s+Serverless\\s+Application\\s+Model\\s*\\(SAM\\)', service_id_var, 105),
    ('SAM_FULL_NO_BRAND_WITH_ACRONYM', 'SERVICE_NAME', 'Serverless\\s+Application\\s+Model\\s*\\(SAM\\)', service_id_var, 100),
    ('SAM_SHORT_AMAZON', 'SERVICE_NAME', 'Amazon\\s+SAM\\b', service_id_var, 95),
    ('SAM_SHORT_AWS', 'SERVICE_NAME', 'AWS\\s+SAM\\b', service_id_var, 95),
    ('SAM_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSAM\\b(?![:\\-])', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Simple Storage Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('S3_FULL', 'SERVICE_NAME', 'Amazon\\s+Simple\\s+Storage\\s+Service\\s*\\(S3\\)', service_id_var, 100),
    ('S3_SHORT', 'SERVICE_NAME', 'Amazon\\s+S3\\b', service_id_var, 95),
    ('S3_ACRONYM_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bS3\\b(?![:_-])', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Simple Storage Service Glacier');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('S3GLACIER_FULL', 'SERVICE_NAME', 'Amazon\\s+Simple\\s+Storage\\s+Service\\s+Glacier\\s*\\(Amazon\\s+S3\\s+Glacier\\)', service_id_var, 100),
    ('S3GLACIER_SHORT', 'SERVICE_NAME', 'Amazon\\s+S3\\s+Glacier', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon DynamoDB');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('DYNAMODB_AMAZON', 'SERVICE_NAME', 'Amazon\\s+DynamoDB', service_id_var, 100),
    ('DYNAMODB_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bDynamoDB\\b(?![:_-])', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon CloudWatch Events');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('CLOUDWATCH_EVENTS', 'SERVICE_NAME', 'Amazon\\s+CloudWatch\\s+Events', service_id_var, 105)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon CloudWatch Logs');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('CLOUDWATCH_LOGS', 'SERVICE_NAME', 'Amazon\\s+CloudWatch\\s+Logs', service_id_var, 105)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon CloudWatch');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('CLOUDWATCH_AMAZON', 'SERVICE_NAME', 'Amazon\\s+CloudWatch(?!\\s+Events|\\s+Logs)', service_id_var, 100),
    ('CLOUDWATCH_ONLY', 'SERVICE_NAME', '\\bCloudWatch\\b(?!\\s+Events|\\s+Logs)', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Simple Notification Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('SNS_FULL', 'SERVICE_NAME', 'Amazon\\s+Simple\\s+Notification\\s+Service\\s*\\(SNS\\)', service_id_var, 100),
    ('SNS_SHORT', 'SERVICE_NAME', 'Amazon\\s+SNS\\b', service_id_var, 95),
    ('SNS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSNS\\b', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Simple Queue Service');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('SQS_FULL', 'SERVICE_NAME', 'Amazon\\s+Simple\\s+Queue\\s+Service\\s*\\(SQS\\)', service_id_var, 100),
    ('SQS_SHORT', 'SERVICE_NAME', 'Amazon\\s+SQS\\b', service_id_var, 95),
    ('SQS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSQS\\b', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Systems Manager');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('SYSTEMS_MANAGER_AWS_FULL', 'SERVICE_NAME', 'AWS\\s+Systems\\s+Manager\\s*\\(SSM\\)', service_id_var, 100),
    ('SYSTEMS_MANAGER_AWS_SHORT', 'SERVICE_NAME', 'AWS\\s+SSM\\b', service_id_var, 95),
    ('SYSTEMS_MANAGER_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Systems\\s+Manager', service_id_var, 100),
    ('SYSTEMS_MANAGER_AWS', 'SERVICE_NAME', 'AWS\\s+Systems\\s+Manager\\b', service_id_var, 95),
    ('SYSTEMS_MANAGER_ONLY', 'SERVICE_NAME', '\\bSystems\\s+Manager\\b', service_id_var, 90),
    ('SSM_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSSM\\b(?!-)', service_id_var, 85)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Batch');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('BATCH_AWS', 'SERVICE_NAME', 'AWS\\s+Batch', service_id_var, 100),
    ('BATCH_AMAZON', 'SERVICE_NAME', 'Amazon\\s+Batch', service_id_var, 100),
    ('BATCH_ONLY', 'SERVICE_NAME', '(?<![:_-])\\bBatch\\b(?![:_-])', service_id_var, 95)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Amazon Command Line Interface');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('CLI_FULL', 'SERVICE_NAME', 'Amazon\\s+Command\\s+Line\\s+Interface\\s*\\(Amazon\\s+CLI\\)', service_id_var, 100),
    ('CLI_AMAZON', 'SERVICE_NAME', 'Amazon\\s+CLI\\b', service_id_var, 95),
    ('CLI_AWS', 'SERVICE_NAME', 'AWS\\s+CLI\\b', service_id_var, 95),
    ('CLI_ONLY', 'SERVICE_NAME', '\\bCLI\\b', service_id_var, 90)
    ON CONFLICT (pattern_name) DO NOTHING;

    service_id_var := (SELECT id FROM service_names WHERE base_name = 'Service Control Policies');
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority) VALUES
    ('SCP_FULL', 'SERVICE_NAME', 'Service\\s+Control\\s+Policies\\s*\\(SCPs\\)', service_id_var, 100),
    ('SCP_AWS_FULL', 'SERVICE_NAME', 'AWS\\s+SCP\\s*\\(Service\\s+control\\s+policies\\)', service_id_var, 100),
    ('SCP_AWS_SHORT', 'SERVICE_NAME', 'AWS\\s+SCP\\b', service_id_var, 95),
    ('SCPS_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSCPs\\b', service_id_var, 90),
    ('SCP_ACRONYM_ONLY', 'SERVICE_NAME', '\\bSCP\\b(?!s)', service_id_var, 85)
    ON CONFLICT (pattern_name) DO NOTHING;

END $$;
-- ====================================================================
-- 8. 数据库统计信息更新
-- ====================================================================

-- 更新表统计信息以优化查询性能
ANALYZE service_names;
ANALYZE brand_term_mappings;
ANALYZE url_mappings;
ANALYZE regex_patterns;
ANALYZE translation_jobs;
ANALYZE feedback_submissions;

-- ====================================================================
-- 9. 验证脚本
-- ====================================================================
-- (验证脚本部分保持不变)
-- ...

-- ====================================================================
-- 脚本执行完成
-- ====================================================================

RAISE NOTICE '=================================================================';
RAISE NOTICE 'CN Mass Email Translator 数据库架构创建完成！';
RAISE NOTICE '版本: 1.1';
RAISE NOTICE '创建时间: %', NOW();
RAISE NOTICE '=================================================================';

```