# 数据库开发指导文档

## 概述

本文档为 CN Mass Email Translator 项目的数据库开发提供详细指导。数据库设计基于"逻辑外化"哲学，将硬编码的业务规则从应用代码中分离到数据库中，提升系统的可维护性和灵活性。

## 核心设计原则

### 1. 逻辑外化 (Externalization of Logic)
- **目标**: 将业务规则（服务名、品牌词、URL映射、正则表达式）从应用代码中分离
- **实现**: 通过数据库表存储规则，支持动态更新而无需重新部署代码
- **受益者**: 语言专家、项目经理等非开发人员可通过管理界面更新翻译规则

### 2. 过程可追溯 (Traceability)
- **核心表**: `translation_jobs` 表记录翻译任务的完整生命周期
- **追踪内容**: 从原始输入到最终输出的每一步结果
- **用途**: 调试、质量审计、算法优化

### 3. 原子化与规范化 (Atomicity & Normalization)
- **策略**: 将不同类型的规则拆分到独立表中
- **核心表**: `brand_term_mappings`, `url_mappings`, `regex_patterns`
- **优势**: 确保数据一致性和管理便捷性

### 4. JSONB 优化使用
- **适用场景**: 半结构化数据（占位符映射、服务提及状态）
- **性能优化**: 使用 GIN 索引支持高效查询
- **灵活性**: 适应动态变化的数据结构

## 数据库表结构详解

### 核心业务逻辑表

#### 1. service_names (服务名称表) - v2 更新版
**用途**: 存储 AWS 服务的标准中英文名称和缩写信息，支持多数据源同步

**v2版本关键字段**:
- `authoritative_full_name`: 来自官网网页的权威服务全称，作为同步操作的唯一业务键
- `base_name`: 用于翻译逻辑的规范化基础名称（不含括号），根据 `authoritative_full_name` 推导
- `internal_name`: 来自PDF文档的 "AWS offering (internal name)"
- `full_name_en`: 首次使用时的英文全称，与 `authoritative_full_name` 保持一致
- `short_name_en`: 后续使用的英文简称，主要来自PDF解析或等于全称
- `full_name_cn/short_name_cn`: 中文全称和简称（预留字段）
- `service_code`: AWS官方服务代码，基于 `internal_name` 生成（转换为小写并处理特殊字符）
- `source`: 数据来源（web_scrape, pdf_parse, manual）
- `last_synced_at`: 上次同步时间，支持增量更新

**字段职责分离**:
- **`authoritative_full_name`**: 负责数据同步，作为与外部数据源匹配的业务主键
- **`base_name`**: 负责翻译逻辑状态跟踪，用于服务提及状态管理
- **`full_name_en`**: 提供首次提及时的替换值
- **`short_name_en`**: 提供后续提及时的替换值

**开发要点**:
- 替代硬编码的 `servicePatterns` 数组
- 支持AWS服务同步系统的自动化数据更新
- 使用 `authoritative_full_name` 作为UPSERT操作的业务主键
- 提供服务名称标准化基础和翻译逻辑支持

#### 2. brand_term_mappings (品牌术语映射表)
**用途**: 存储需要强制替换的品牌术语

**典型映射**:
- `Amazon Web Services Support` → `亚马逊云科技中国支持团队`
- `Amazon console` → `亚马逊云科技控制台`
- `Amazon account` → `亚马逊云科技账户`

**开发要点**:
- 支持动态启用/禁用规则
- 确保术语替换的一致性
- 简化品牌术语维护流程

#### 3. url_mappings (URL映射规则表)
**用途**: 存储 URL 本地化替换规则

**关键特性**:
- `is_regex`: 支持正则表达式模式
- `priority`: 优先级控制（数字越大优先级越高）
- 典型映射: `aws.amazon.com` → `amazonaws.cn`

**开发要点**:
- 处理重叠规则的优先级
- 支持复杂的 URL 转换逻辑
- 配置化 URL 本地化流程

#### 4. regex_patterns (正则表达式模式表)
**用途**: 存储用于实体识别的正则表达式，基于全新的8种核心模式类型系统

**v2版本关键字段**:
- `pattern_name`: 模式的易读名称，如 "EC2_FULL_COMPLEX_SUFFIX"
- `pattern_type`: 模式类型（SERVICE_NAME, TIMEZONE, CLI_COMMAND, URL, GENERAL）
- `regex_string`: 正则表达式本身，支持边界保护和复合后缀
- `related_service_id`: 关联的服务ID（外键到service_names表）
- `service_code`: 服务代码，如 "ec2", "s3"，便于按服务分组管理
- `priority`: 匹配优先级，范围90-130，数字越大优先级越高
- `notes`: 模式元数据，如 "isCompoundWithSuffix: true, suffixGroup: 1"
- `validation_status`: 验证状态（pending, valid, invalid）

**8种核心模式类型**:
1. **全称复合后缀模式** (优先级: 120) - 匹配完整服务名称 + 复杂后缀
2. **全称标准模式** (优先级: 115) - 精确匹配完整服务名称，带边界保护
3. **简称复合后缀模式** (优先级: 110) - 匹配简称 + 复杂后缀
4. **简称标准模式** (优先级: 105) - 精确匹配服务简称，带边界保护
5. **缩写复合后缀模式** (优先级: 100) - 匹配纯缩写 + 后缀
6. **缩写标准模式** (优先级: 95) - 精确匹配纯缩写，带边界保护
7. **特殊变体模式** (优先级: 125) - 处理特殊服务变体（如Aurora PostgreSQL）
8. **上下文保护模式** (优先级: 90) - 在特定上下文中避免误匹配

**边界保护机制**:
- **ARN保护**: 使用负向前瞻避免ARN中的服务名匹配
- **URL保护**: 避免URL路径中的服务名误匹配
- **代码保护**: 防止代码块中的服务名被错误识别

**开发要点**:
- 完全替代硬编码的正则表达式数组
- 支持复合词智能处理和后缀捕获
- 实现分层优先级系统（90-130）
- 提供特殊服务（Aurora、Health、RDS等）的专门处理
- 集成边界保护机制避免误匹配

### 核心工作流表

#### 5. translation_jobs (翻译任务表)
**用途**: 记录翻译任务的完整生命周期

**4阶段追踪**:
- **Stage 0**: 原始文本 (`original_text`)
- **Stage 1**: JS标准化后文本 (`stage1_standardized_en`)
- **Stage 2**: LLM输入输出 (`stage2_llm_input`, `stage2_llm_output`)
- **Stage 3**: 最终中文结果 (`stage3_final_cn`)

**JSONB字段**:
- `placeholder_map`: 占位符映射 `{"__SRVCNM_0__": "Amazon EC2"}`
- `service_mention_state`: 服务提及状态追踪

**开发要点**:
- 使用 UUID 主键支持分布式系统
- 提供完整的可追溯性
- 支持错误定位和性能分析

#### 6. feedback_submissions (用户反馈表)
**用途**: 存储用户反馈数据

**集成特性**:
- 与 `translation_jobs` 关联（可选）
- S3 存储集成 (`s3_object_key`)
- 满意度枚举管理

## AWS服务同步系统集成指导

### 核心原则：功能精简
**🎯 重要**: AWS服务同步系统的所有子功能开发都应该**只实现功能本身**，遵循以下原则：
- ❌ 不要添加复杂的监控和性能统计功能
- ❌ 不要添加详细的报告生成和分析功能  
- ❌ 不要添加复杂的测试套件和验证工具
- ❌ 不要添加过度的日志记录和指标收集
- ✅ 只实现核心业务功能和基本错误处理
- ✅ 使用简单的日志记录（基本的成功/失败状态）
- ✅ 保持代码简洁和可维护性

### 系统架构集成

#### 模块结构
```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端
└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

#### 数据库集成要点
1. **使用authoritative_full_name作为业务主键**进行UPSERT操作
2. **自动生成8种核心模式类型**，优先级范围90-130
3. **实现边界保护机制**避免ARN、URL等上下文中的误匹配
4. **支持特殊服务处理**（Aurora、Health、RDS等）

#### Lambda函数集成示例
```python
def lambda_handler(event, context):
    """AWS服务同步Lambda函数入口 - 保持简洁"""
    try:
        logger.info("开始执行服务同步")
        
        # 1. 初始化配置
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # 2. 抓取和处理数据
        web_scraper = WebScraper()
        pdf_parser = PDFParser(config)
        data_processor = DataProcessor()
        
        web_services = web_scraper.scrape_services()
        pdf_services = pdf_parser.parse_pdf()
        merged_services = data_processor.process_services(web_services, pdf_services)
        
        # 3. 存储到数据库
        rds_client = RDSClient(config)
        stored_count = rds_client.store_services(merged_services)
        
        # 4. 生成和存储正则表达式模式
        all_patterns = batch_generate_all_patterns(merged_services)
        validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)
        
        logger.info(f"同步完成: {stored_count}个服务, {validated_count}个模式")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'processed_count': stored_count,
                'pattern_count': validated_count
            })
        }
        
    except Exception as e:
        logger.error(f"同步失败: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({'success': False, 'error': str(e)})
        }
```

## 开发最佳实践

### 1. 数据库连接和事务管理
```python
# 使用连接池
import psycopg2.pool

# 事务管理示例
def update_service_with_transaction(service_data):
    conn = None
    try:
        conn = get_db_connection()
        with conn:
            with conn.cursor() as cur:
                # 执行更新操作
                cur.execute(update_sql, service_data)
                # 自动提交
    except Exception as e:
        # 自动回滚
        logger.error(f"Database update failed: {e}")
        raise
    finally:
        if conn:
            conn.close()
```

### 2. JSONB 字段操作
```sql
-- 查询占位符映射
SELECT id, placeholder_map->'__SRVCNM_0__' as service_name
FROM translation_jobs
WHERE placeholder_map ? '__SRVCNM_0__';

-- 更新服务提及状态
UPDATE translation_jobs
SET service_mention_state = service_mention_state || '{"Amazon EC2": {"mentioned": true}}'::jsonb
WHERE id = $1;
```

### 3. 优先级查询模式
```sql
-- 按优先级获取URL映射规则
SELECT source_pattern, target_pattern, is_regex
FROM url_mappings
WHERE is_active = true
ORDER BY priority DESC, id ASC;

-- 按优先级获取正则表达式模式
SELECT pattern_name, regex_string, related_service_id
FROM regex_patterns
WHERE pattern_type = 'SERVICE_NAME' AND is_active = true
ORDER BY priority DESC, id ASC;
```

### 4. 批量操作优化 (v2 更新版)
```python
# 批量插入服务名称 (v2版本 - 支持AWS服务同步)
def batch_upsert_services(services_data):
    """
    批量插入或更新服务名称，使用authoritative_full_name作为业务主键
    
    Args:
        services_data: List of tuples containing:
            (authoritative_full_name, base_name, internal_name, full_name_en, 
             short_name_en, service_code, source)
    """
    insert_sql = """
    INSERT INTO service_names 
        (authoritative_full_name, base_name, internal_name, full_name_en, 
         short_name_en, service_code, source, last_synced_at)
    VALUES %s
    ON CONFLICT (authoritative_full_name) 
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.full_name_en,
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    """
    
    # 为每个记录添加当前时间戳
    timestamped_data = [
        (*record, 'NOW()') for record in services_data
    ]
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            psycopg2.extras.execute_values(
                cur, insert_sql, timestamped_data, template=None, page_size=100
            )

# AWS服务同步专用的数据库操作
def sync_services_from_web_and_pdf(web_data, pdf_data):
    """
    同步来自网页和PDF的服务数据
    
    Args:
        web_data: List of service names from web scraping
        pdf_data: List of service details from PDF parsing
    """
    matched_services = []
    
    for web_service in web_data:
        # 查找匹配的PDF数据
        pdf_match = find_pdf_match(web_service, pdf_data)
        
        # 生成base_name（移除括号内容）
        base_name = re.sub(r'\s*\([^)]*\)', '', web_service).strip()
        
        service_record = (
            web_service,  # authoritative_full_name
            base_name,    # base_name
            pdf_match.get('internal_name') if pdf_match else None,
            web_service,  # full_name_en (与authoritative_full_name相同)
            pdf_match.get('short_name') if pdf_match else web_service,
            generate_service_code(pdf_match.get('internal_name')) if pdf_match else None,
            'web_scrape'  # source
        )
        
        matched_services.append(service_record)
    
    # 批量更新数据库
    batch_upsert_services(matched_services)
    
    return len(matched_services)

# 新版本：基于8种核心模式类型的完整模式生成系统
def generate_comprehensive_service_patterns(service_data):
    """
    为单个服务生成完整的8种核心模式集合
    基于最新的AWS服务模式生成系统设计规范
    
    Args:
        service_data: 服务数据字典，包含所有必要字段
    
    Returns:
        List[dict]: 生成的模式列表
    """
    patterns = []
    service_id = service_data['id']
    full_name = service_data['full_name_en']
    short_name = service_data['short_name_en']
    service_code = service_data['service_code']
    acronym = get_acronym(full_name)
    
    # 1. 全称复合后缀模式 (优先级: 120)
    if has_complex_suffix_support(service_data):
        suffix_patterns = get_service_suffix_patterns(service_code)
        patterns.append({
            'pattern_name': f"{acronym}_FULL_COMPLEX_SUFFIX",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"{re.escape(full_name)}\\s+({suffix_patterns})",
            'related_service_id': service_id,
            'service_code': service_code,
            'priority': 120,
            'notes': 'isCompoundWithSuffix: true, suffixGroup: 1',
            'validation_status': 'valid'
        })
    
    # 2. 全称标准模式 (优先级: 115) - 带边界保护
    patterns.append({
        'pattern_name': f"{acronym}_FULL_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(full_name),
        'related_service_id': service_id,
        'service_code': service_code,
        'priority': 115,
        'notes': 'Standard full name pattern with boundary protection',
        'validation_status': 'valid'
    })
    
    # 3. 简称复合后缀模式 (优先级: 110)
    if has_complex_suffix_support(service_data):
        patterns.append({
            'pattern_name': f"{acronym}_SHORT_COMPLEX_SUFFIX",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"{re.escape(short_name)}\\s+({suffix_patterns})",
            'related_service_id': service_id,
            'service_code': service_code,
            'priority': 110,
            'notes': 'isCompoundWithSuffix: true, suffixGroup: 1',
            'validation_status': 'valid'
        })
    
    # 4. 简称标准模式 (优先级: 105) - 带边界保护
    patterns.append({
        'pattern_name': f"{acronym}_SHORT_STANDARD",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_boundary_protected_pattern(short_name),
        'related_service_id': service_id,
        'service_code': service_code,
        'priority': 105,
        'notes': 'Standard short name pattern with boundary protection',
        'validation_status': 'valid'
    })
    
    # 5. 缩写复合后缀模式 (优先级: 100)
    if acronym and has_complex_suffix_support(service_data):
        patterns.append({
            'pattern_name': f"{acronym}_ACRONYM_COMPLEX_SUFFIX",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?<![:_-])\\b{acronym}\\s+({suffix_patterns})(?![:_-])",
            'related_service_id': service_id,
            'service_code': service_code,
            'priority': 100,
            'notes': 'isCompoundWithSuffix: true, suffixGroup: 1',
            'validation_status': 'valid'
        })
    
    # 6. 缩写标准模式 (优先级: 95) - 带边界保护
    if acronym:
        patterns.append({
            'pattern_name': f"{acronym}_ACRONYM_STANDARD",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?<![:_-])\\b{acronym}\\b(?![:_-])",
            'related_service_id': service_id,
            'service_code': service_code,
            'priority': 95,
            'notes': 'Pure acronym pattern with boundary protection',
            'validation_status': 'valid'
        })
    
    # 7. 特殊变体模式 (优先级: 125)
    if has_special_variants(service_data):
        variant_patterns = generate_special_variant_patterns(service_data)
        patterns.extend(variant_patterns)
    
    # 8. 上下文保护模式 (优先级: 90)
    context_patterns = generate_context_aware_patterns(service_data)
    patterns.extend(context_patterns)
    
    return patterns

# 辅助函数集合
def get_acronym(service_name):
    """从服务名称中提取缩写"""
    match = re.search(r'\(([^)]+)\)', service_name)
    if match:
        return match.group(1)
    
    # 如果没有括号，尝试从首字母生成
    words = service_name.replace('Amazon ', '').replace('AWS ', '').split()
    return ''.join([word[0].upper() for word in words if word[0].isupper()])

def generate_boundary_protected_pattern(service_name):
    """生成带完整边界保护的模式"""
    # ARN保护：避免在ARN中匹配
    arn_protection = r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)'
    
    # URL保护：避免在URL中匹配
    url_protection = r'(?<!https?://[^\s]*)'
    
    # 代码保护：避免在代码标识符中匹配
    code_protection = r'(?<![:_-])'
    
    # 后续保护：避免作为更大标识符的一部分
    trailing_protection = r'(?![:_-])'
    url_trailing_protection = r'(?![^\s]*\.[a-z]{2,4})'
    
    return f"{arn_protection}{url_protection}{code_protection}\\b{re.escape(service_name)}\\b{trailing_protection}{url_trailing_protection}"

def get_service_suffix_patterns(service_code):
    """根据服务代码获取适用的后缀模式"""
    COMMON_SUFFIX_PATTERNS = {
        'instance': r'((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))',
        'volume': r'(volume\(s\)?|volumes|volume)',
        'database': r'(for\s+(?:PostgreSQL|MySQL|MariaDB|Oracle|SQL\s+Server))',
        'cluster': r'(cluster|clusters)',
        'service': r'(service|services)',
        'resource': r'(resource|resources)'
    }
    
    suffix_mapping = {
        'ec2': ['instance', 'volume'],
        'rds': ['database', 'cluster'],
        'ecs': ['cluster', 'service'],
        'eks': ['cluster'],
        's3': ['resource'],
        'lambda': ['resource']
    }
    
    applicable_suffixes = suffix_mapping.get(service_code, ['resource'])
    return '|'.join([COMMON_SUFFIX_PATTERNS[suffix] for suffix in applicable_suffixes])

def has_complex_suffix_support(service_data):
    """判断服务是否支持复杂后缀"""
    suffix_supported_services = ['ec2', 'rds', 'ecs', 'eks', 'ebs']
    return service_data['service_code'] in suffix_supported_services

def has_special_variants(service_data):
    """判断服务是否有特殊变体"""
    variant_services = ['aurora', 'rds', 'health', 'iam']
    return service_data['service_code'] in variant_services

def generate_special_variant_patterns(service_data):
    """生成特殊变体模式"""
    patterns = []
    service_code = service_data['service_code']
    
    # Aurora服务族的特殊处理
    if service_code == 'aurora':
        for engine in ['PostgreSQL', 'MySQL']:
            patterns.append({
                'pattern_name': f'AURORA_{engine.upper()}_VARIANT',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{engine}\\b",
                'related_service_id': service_data['id'],
                'service_code': service_code,
                'priority': 125,
                'notes': f'Aurora {engine} specific variant',
                'validation_status': 'valid'
            })
    
    # RDS服务族的特殊处理
    elif service_code == 'rds':
        engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
        for engine in engines:
            patterns.append({
                'pattern_name': f'RDS_FOR_{engine.replace(" ", "_").upper()}',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
                'related_service_id': service_data['id'],
                'service_code': service_code,
                'priority': 125,
                'notes': f'RDS for {engine} specific pattern',
                'validation_status': 'valid'
            })
    
    return patterns

def generate_context_aware_patterns(service_data):
    """生成上下文感知的模式"""
    patterns = []
    service_code = service_data['service_code']
    acronym = get_acronym(service_data['full_name_en'])
    
    # CLI命令上下文
    if service_code:
        patterns.append({
            'pattern_name': f"{acronym}_CLI_CONTEXT",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?<=aws\\s){service_code}(?=\\s)",
            'related_service_id': service_data['id'],
            'service_code': service_code,
            'priority': 130,
            'notes': 'CLI command context pattern',
            'validation_status': 'valid'
        })
    
    # 配置文件上下文
    patterns.append({
        'pattern_name': f"{acronym}_CONFIG_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=\"){service_code}(?=\")",
        'related_service_id': service_data['id'],
        'service_code': service_code,
        'priority': 128,
        'notes': 'Configuration file context pattern',
        'validation_status': 'valid'
    })
    
    return patterns

# 新版本：优化的批量模式插入，支持验证和冲突检测
def batch_insert_patterns_optimized(patterns):
    """
    优化的批量模式插入，基于最新的AWS服务模式生成系统
    
    Args:
        patterns: List[dict] - 模式列表，每个模式包含完整的字段信息
    
    Returns:
        Tuple[int, List[dict]] - (验证通过的模式数量, 检测到的冲突列表)
    """
    # 验证所有模式的语法正确性
    validated_patterns = []
    for pattern in patterns:
        is_valid, error = validate_pattern_syntax(pattern)
        if is_valid:
            pattern['validation_status'] = 'valid'
            validated_patterns.append(pattern)
        else:
            logger.warning(f"Invalid pattern {pattern['pattern_name']}: {error}")
            pattern['validation_status'] = 'invalid'
    
    # 检测模式冲突
    conflicts = detect_pattern_conflicts(validated_patterns)
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")
    
    # 批量插入到数据库
    insert_sql = """
    INSERT INTO regex_patterns 
        (pattern_name, pattern_type, regex_string, related_service_id, 
         service_code, priority, notes, validation_status, is_active)
    VALUES %s
    ON CONFLICT (pattern_name) 
    DO UPDATE SET
        regex_string = EXCLUDED.regex_string,
        priority = EXCLUDED.priority,
        notes = EXCLUDED.notes,
        validation_status = EXCLUDED.validation_status,
        service_code = EXCLUDED.service_code,
        updated_at = NOW()
    """
    
    pattern_data = [
        (p['pattern_name'], p['pattern_type'], p['regex_string'], 
         p['related_service_id'], p['service_code'], p['priority'], 
         p['notes'], p['validation_status'], p.get('is_active', True))
        for p in validated_patterns
    ]
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            psycopg2.extras.execute_values(
                cur, insert_sql, pattern_data, template=None, page_size=100
            )
    
    return len(validated_patterns), conflicts

def validate_pattern_syntax(pattern):
    """验证正则表达式模式的语法正确性"""
    try:
        re.compile(pattern['regex_string'])
        return True, None
    except re.error as e:
        return False, str(e)

def detect_pattern_conflicts(patterns):
    """检测模式之间的潜在冲突"""
    conflicts = []
    
    for i, pattern1 in enumerate(patterns):
        for j, pattern2 in enumerate(patterns[i+1:], i+1):
            if pattern1['priority'] == pattern2['priority']:
                conflicts.append({
                    'type': 'priority_conflict',
                    'patterns': [pattern1['pattern_name'], pattern2['pattern_name']],
                    'priority': pattern1['priority']
                })
    
    return conflicts

# 批量生成所有服务的模式
def batch_generate_all_patterns(services_list):
    """
    批量生成所有服务的完整模式集合
    
    Args:
        services_list: List[dict] - 服务数据列表
    
    Returns:
        List[dict] - 所有生成的模式列表
    """
    all_patterns = []
    
    for service_data in services_list:
        service_patterns = generate_comprehensive_service_patterns(service_data)
        
        # 添加服务关联信息和时间戳
        for pattern in service_patterns:
            pattern['created_at'] = datetime.now()
            pattern['is_active'] = True
        
        all_patterns.extend(service_patterns)
    
    # 按优先级排序（数值越大优先级越高）
    all_patterns.sort(key=lambda x: x['priority'], reverse=True)
    
    return all_patterns
```

## 性能优化指导

### 1. 索引使用策略
- **GIN索引**: 用于 JSONB 字段高效查询
- **复合索引**: 优化多条件查询
- **部分索引**: 针对活跃数据的条件索引

### 2. 查询优化技巧
```sql
-- 使用 EXPLAIN ANALYZE 分析查询性能
EXPLAIN ANALYZE
SELECT s.base_name, r.regex_string
FROM service_names s
JOIN regex_patterns r ON s.id = r.related_service_id
WHERE s.is_active = true AND r.pattern_type = 'SERVICE_NAME';

-- 使用适当的 LIMIT 避免大结果集
SELECT * FROM translation_jobs
WHERE status = 'completed'
ORDER BY submitted_at DESC
LIMIT 100;
```

### 3. 连接池配置
```python
# 推荐的连接池配置
DB_POOL_CONFIG = {
    'minconn': 5,
    'maxconn': 20,
    'host': os.getenv('DB_HOST'),
    'database': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'port': 5432
}
```

## 数据迁移和维护

### 1. 数据迁移脚本模板
```sql
-- 迁移脚本示例
BEGIN;

-- 1. 备份现有数据
CREATE TABLE service_names_backup AS SELECT * FROM service_names;

-- 2. 执行结构变更
ALTER TABLE service_names ADD COLUMN new_field VARCHAR(100);

-- 3. 数据转换
UPDATE service_names SET new_field = 'default_value' WHERE new_field IS NULL;

-- 4. 验证数据完整性
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM service_names WHERE new_field IS NULL) > 0 THEN
        RAISE EXCEPTION 'Data migration validation failed';
    END IF;
END $$;

COMMIT;
```

### 2. 定期维护任务
```sql
-- 清理过期的翻译任务记录
DELETE FROM translation_jobs
WHERE status = 'completed'
  AND completed_at < NOW() - INTERVAL '90 days';

-- 更新统计信息
ANALYZE service_names;
ANALYZE translation_jobs;
ANALYZE feedback_submissions;

-- 重建索引（如需要）
REINDEX INDEX CONCURRENTLY idx_translation_jobs_submitted_at;
```

## 监控和告警

### 1. 关键指标监控
- 数据库连接数
- 查询执行时间
- 表大小增长趋势
- 索引使用率
- JSONB 字段查询性能

### 2. 告警阈值设置
```python
# 监控配置示例
MONITORING_THRESHOLDS = {
    'connection_count': 80,  # 连接数超过80%
    'query_time_ms': 5000,   # 查询时间超过5秒
    'table_size_gb': 10,     # 表大小超过10GB
    'error_rate': 0.05       # 错误率超过5%
}
```

## 安全性考虑

### 1. 访问控制
```sql
-- 创建应用专用用户
CREATE USER mass_email_app WITH PASSWORD 'secure_password';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE ON service_names TO mass_email_app;
GRANT SELECT, INSERT, UPDATE ON translation_jobs TO mass_email_app;
GRANT SELECT ON regex_patterns TO mass_email_app;

-- 限制敏感操作
REVOKE DELETE ON service_names FROM mass_email_app;
```

### 2. 数据保护
- 启用 SSL/TLS 连接加密
- 定期备份关键数据
- 实施数据保留策略
- 监控异常访问模式

## 故障排查指南

### 1. 常见问题诊断
```sql
-- 检查数据库连接状态
SELECT state, count(*) FROM pg_stat_activity GROUP BY state;

-- 检查长时间运行的查询
SELECT pid, now() - pg_stat_activity.query_start AS duration, query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- 检查表大小
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. 性能问题排查
```sql
-- 检查索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 检查慢查询
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

## 开发工作流集成

### 1. 本地开发环境
```bash
# Docker Compose 配置示例
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: mass_email_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - ./database/ddl3.sql:/docker-entrypoint-initdb.d/init.sql
```

### 2. 测试数据准备
```python
# 测试数据生成脚本
def create_test_data():
    test_services = [
        ('Amazon Elastic Compute Cloud', 'Amazon Elastic Compute Cloud (EC2)', 'Amazon EC2'),
        ('Amazon Simple Storage Service', 'Amazon Simple Storage Service (S3)', 'Amazon S3'),
    ]
    
    test_brand_terms = [
        ('Amazon Web Services Support', '亚马逊云科技中国支持团队'),
        ('Amazon console', '亚马逊云科技控制台'),
    ]
    
    # 插入测试数据...
```

## 生产数据库初始化

### 1. 完整的初始化数据集
基于 `database/mass_email_database_schema_v1.sql`，数据库包含了从POC代码转换而来的完整生产级初始化数据：

#### 品牌术语映射（18个核心规则）
```sql
-- 示例数据
INSERT INTO brand_term_mappings (term_en, term_cn, notes) VALUES
('Amazon Web Services Support', '亚马逊云科技中国支持团队', '标准替换'),
('AWS Accounts Teams', 'Amazon Web Services Support', '标准化为官方支持'),
('AWS Account', 'Amazon Web Services Account', '标准化为官方账户'),
('AWS Support Center', 'Amazon Web Services Support', '标准化为官方支持');
```

#### URL映射规则（8个高优先级规则）
```sql
-- 示例数据
INSERT INTO url_mappings (source_pattern, target_pattern, priority, is_regex, notes) VALUES
('^https?:\/\/amazonaws\.cn\/support\/?$', 'https://console.amazonaws.cn/support/', 200, TRUE, '修正错误的 .cn/support 链接'),
('^https?:\/\/aws\.amazon\.com\/(support|contact-us)\/?$', 'https://console.amazonaws.cn/support/', 200, TRUE, '将旧的 support/contact-us 链接统一到新的 .cn 控制台');
```

#### AWS服务名称（25个核心服务）
```sql
-- 示例数据
INSERT INTO service_names (base_name, full_name_en, short_name_en, service_code) VALUES
('Amazon Elastic Compute Cloud', 'Amazon Elastic Compute Cloud (EC2)', 'Amazon EC2', 'ec2'),
('Amazon Simple Storage Service', 'Amazon Simple Storage Service (S3)', 'Amazon S3', 's3'),
('AWS Lambda', 'AWS Lambda', 'Lambda', 'lambda');
```

#### 正则表达式模式（100+个精确规则）
```sql
-- 示例数据（EC2服务的完整模式集）
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id, priority, notes) VALUES
('EC2_FULL_WITH_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\s*((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))', service_id_var, 110, 'isCompoundWithSuffix: true, suffixGroup: 1'),
('EC2_FULL', 'SERVICE_NAME', 'Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)', service_id_var, 105, ''),
('EC2_SHORT_WITH_SUFFIX', 'SERVICE_NAME', 'Amazon\\s+EC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))', service_id_var, 100, 'isCompoundWithSuffix: true, suffixGroup: 1');
```

### 2. 优先级系统实现
- **数值越大优先级越高**：确保精确匹配优先于模糊匹配
- **复合模式支持**：通过 `notes` 字段的 `isCompoundWithSuffix` 标识复合模式
- **外键关联**：所有正则表达式都与对应的服务名称记录关联

### 3. 脚本幂等性保证
```sql
-- 清空现有数据以确保脚本可重复执行
TRUNCATE TABLE brand_term_mappings, url_mappings, regex_patterns, service_names RESTART IDENTITY CASCADE;

-- 使用 ON CONFLICT DO NOTHING 避免重复插入错误
INSERT INTO service_names (...) VALUES (...) ON CONFLICT (base_name) DO NOTHING;
```

### 4. 数据验证和完整性检查
```sql
-- 验证所有表是否创建成功
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY['service_names', 'brand_term_mappings', 'url_mappings', 'regex_patterns', 'translation_jobs', 'feedback_submissions'];
BEGIN
    -- 验证逻辑...
END $$;
```

## 高级查询模式

### 1. 服务名称智能匹配
```sql
-- 按优先级获取服务匹配规则
WITH service_patterns AS (
    SELECT 
        rp.regex_string,
        rp.priority,
        sn.base_name,
        sn.full_name_en,
        sn.short_name_en
    FROM regex_patterns rp
    JOIN service_names sn ON rp.related_service_id = sn.id
    WHERE rp.pattern_type = 'SERVICE_NAME' 
      AND rp.is_active = true 
      AND sn.is_active = true
    ORDER BY rp.priority DESC, rp.id ASC
)
SELECT * FROM service_patterns;
```

### 2. 复合模式处理
```sql
-- 识别支持后缀的复合模式
SELECT 
    pattern_name,
    regex_string,
    notes
FROM regex_patterns
WHERE notes LIKE '%isCompoundWithSuffix: true%'
  AND pattern_type = 'SERVICE_NAME'
  AND is_active = true
ORDER BY priority DESC;
```

### 3. 品牌术语批量替换
```sql
-- 获取所有活跃的品牌术语映射
SELECT 
    term_en,
    term_cn,
    LENGTH(term_en) as term_length
FROM brand_term_mappings
WHERE is_active = true
ORDER BY term_length DESC; -- 长术语优先替换
```

## 边界保护机制详解

### 核心保护策略
基于最新的AWS服务模式生成系统，实现了多层边界保护机制：

#### 1. ARN保护机制
```python
# ARN保护：避免在ARN字符串中匹配服务名
arn_protection = r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)'

# 示例：避免在以下ARN中匹配"EC2"
# arn:aws:ec2:us-west-2:123456789012:instance/i-1234567890abcdef0
```

#### 2. URL保护机制
```python
# URL保护：避免在URL路径中匹配服务名
url_protection = r'(?<!https?://[^\s]*)'
url_trailing_protection = r'(?![^\s]*\.[a-z]{2,4})'

# 示例：避免在以下URL中匹配服务名
# https://console.aws.amazon.com/ec2/
# https://docs.aws.amazon.com/s3/
```

#### 3. 代码标识符保护
```python
# 代码保护：避免在代码标识符中匹配
code_protection = r'(?<![:_-])'
trailing_protection = r'(?![:_-])'

# 示例：避免在以下代码中匹配
# my_ec2_instance, EC2-INSTANCE, namespace::EC2
```

### 特殊服务处理策略

#### Aurora服务族处理
```python
def generate_aurora_patterns():
    """Aurora服务的特殊模式生成"""
    patterns = []
    
    # 通用Aurora模式（排除特定引擎）
    patterns.append({
        'pattern_name': 'AURORA_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
        'priority': 120,
        'notes': 'General Aurora pattern excluding specific variants'
    })
    
    # 特定引擎变体
    for engine in ['PostgreSQL', 'MySQL']:
        patterns.append({
            'pattern_name': f'AURORA_{engine.upper()}_VARIANT',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{engine}\\b",
            'priority': 125,
            'notes': f'Aurora {engine} specific variant'
        })
    
    return patterns
```

#### Health服务族处理
```python
def generate_health_patterns():
    """Health服务的特殊模式生成"""
    return [
        {
            'pattern_name': 'HEALTH_DASHBOARD_FULL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\s+Dashboard',
            'priority': 125,
            'notes': 'Full Health Dashboard pattern'
        },
        {
            'pattern_name': 'HEALTH_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\b(?!Lake|Imaging|\s+Dashboard)',
            'priority': 120,
            'notes': 'General Health pattern excluding other Health services'
        }
    ]
```

#### RDS服务族处理
```python
def generate_rds_patterns():
    """RDS服务的特殊模式生成"""
    engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
    patterns = []
    
    # RDS for Engine模式
    for engine in engines:
        patterns.append({
            'pattern_name': f'RDS_FOR_{engine.replace(" ", "_").upper()}',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
            'priority': 125,
            'notes': f'RDS for {engine} specific pattern'
        })
    
    # 通用RDS模式（排除特定引擎）
    engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
    patterns.append({
        'pattern_name': 'RDS_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
        'priority': 115,
        'notes': 'General RDS pattern excluding specific engine variants'
    })
    
    return patterns
```

## 新增索引优化策略

### 针对v2架构的索引优化
```sql
-- 正则表达式模式表的优化索引
CREATE INDEX idx_regex_patterns_priority_optimized ON regex_patterns(priority DESC, id ASC);
CREATE INDEX idx_regex_patterns_service_code_active ON regex_patterns(service_code, is_active) WHERE is_active = true;
CREATE INDEX idx_regex_patterns_validation_status ON regex_patterns(validation_status);

-- 服务名称表的新增索引
CREATE INDEX idx_service_names_authoritative_name ON service_names(authoritative_full_name);
CREATE INDEX idx_service_names_last_synced ON service_names(last_synced_at) WHERE last_synced_at IS NOT NULL;
CREATE INDEX idx_service_names_source_active ON service_names(source, is_active);
```

### 查询性能优化示例
```sql
-- 优化的服务模式查询
WITH prioritized_patterns AS (
    SELECT 
        rp.pattern_name,
        rp.regex_string,
        rp.priority,
        rp.notes,
        sn.authoritative_full_name,
        sn.service_code
    FROM regex_patterns rp
    JOIN service_names sn ON rp.related_service_id = sn.id
    WHERE rp.pattern_type = 'SERVICE_NAME' 
      AND rp.validation_status = 'valid'
      AND rp.is_active = true 
      AND sn.is_active = true
    ORDER BY rp.priority DESC, rp.id ASC
)
SELECT * FROM prioritized_patterns;

-- 复合模式的高效查询
SELECT 
    pattern_name,
    regex_string,
    priority,
    CASE 
        WHEN notes LIKE '%isCompoundWithSuffix: true%' THEN 
            regexp_replace(notes, '.*suffixGroup: (\d+).*', '\1')::int
        ELSE NULL 
    END as suffix_group
FROM regex_patterns
WHERE pattern_type = 'SERVICE_NAME'
  AND validation_status = 'valid'
  AND is_active = true
ORDER BY priority DESC, suffix_group NULLS LAST;
```

## 最新开发工作流集成

### 1. 完整的模式生成工作流
```python
def complete_pattern_generation_workflow():
    """完整的模式生成工作流示例"""
    
    # 1. 获取所有活跃服务
    services = get_all_active_services()
    logger.info(f"Found {len(services)} active services")
    
    # 2. 生成所有模式
    all_patterns = batch_generate_all_patterns(services)
    logger.info(f"Generated {len(all_patterns)} patterns")
    
    # 3. 验证和优化
    validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)
    
    # 4. 生成报告
    report = {
        'total_services': len(services),
        'total_patterns': len(all_patterns),
        'validated_patterns': validated_count,
        'conflicts_detected': len(conflicts),
        'pattern_types': {
            'full_complex_suffix': len([p for p in all_patterns if 'FULL_COMPLEX_SUFFIX' in p['pattern_name']]),
            'full_standard': len([p for p in all_patterns if 'FULL_STANDARD' in p['pattern_name']]),
            'short_complex_suffix': len([p for p in all_patterns if 'SHORT_COMPLEX_SUFFIX' in p['pattern_name']]),
            'short_standard': len([p for p in all_patterns if 'SHORT_STANDARD' in p['pattern_name']]),
            'acronym_complex_suffix': len([p for p in all_patterns if 'ACRONYM_COMPLEX_SUFFIX' in p['pattern_name']]),
            'acronym_standard': len([p for p in all_patterns if 'ACRONYM_STANDARD' in p['pattern_name']]),
            'special_variants': len([p for p in all_patterns if p['priority'] == 125]),
            'context_aware': len([p for p in all_patterns if 'CONTEXT' in p['pattern_name']])
        }
    }
    
    logger.info(f"Pattern generation completed: {report}")
    return report
```

### 2. 数据库健康检查
```python
def database_health_check():
    """数据库健康状态检查"""
    checks = []
    
    # 检查表结构完整性
    required_tables = ['service_names', 'regex_patterns', 'brand_term_mappings', 'url_mappings']
    for table in required_tables:
        count = execute_query(f"SELECT COUNT(*) FROM {table}")[0][0]
        checks.append({
            'check': f'{table}_exists',
            'status': 'pass' if count >= 0 else 'fail',
            'details': f'{count} records'
        })
    
    # 检查模式验证状态
    invalid_patterns = execute_query("""
        SELECT COUNT(*) FROM regex_patterns 
        WHERE validation_status = 'invalid'
    """)[0][0]
    
    checks.append({
        'check': 'pattern_validation',
        'status': 'pass' if invalid_patterns == 0 else 'warning',
        'details': f'{invalid_patterns} invalid patterns'
    })
    
    # 检查优先级冲突
    priority_conflicts = execute_query("""
        SELECT priority, COUNT(*) as conflict_count
        FROM regex_patterns 
        WHERE is_active = true
        GROUP BY priority 
        HAVING COUNT(*) > 1
    """)
    
    checks.append({
        'check': 'priority_conflicts',
        'status': 'pass' if len(priority_conflicts) == 0 else 'warning',
        'details': f'{len(priority_conflicts)} priority conflicts'
    })
    
    return checks
```

## 总结

这份更新后的数据库开发指导文档现在完全反映了最新的系统架构和设计理念：

### 主要更新内容
1. **8种核心模式类型系统** - 完全替代了原有的4种基本模式
2. **分层优先级系统** - 优先级范围从90-130，提供精确控制
3. **边界保护机制** - ARN、URL、代码标识符的多重保护
4. **特殊服务处理** - Aurora、Health、RDS等服务的专门处理策略
5. **AWS服务同步系统集成** - 功能精简原则和完整的集成指导
6. **优化的数据库操作** - 支持验证、冲突检测的批量操作
7. **增强的索引策略** - 针对v2架构的性能优化

### 核心优势
- **全面性**: 覆盖所有常见使用场景和边缘情况
- **精确性**: 通过边界保护和优先级控制避免误匹配
- **精简性**: 遵循功能精简原则，保持代码简洁
- **可维护性**: 模块化设计和完整的文档支持
- **性能优化**: 针对大规模数据的查询和操作优化

这份指导文档为数据库开发提供了全面的指导，确保开发团队能够正确实施"逻辑外化"的设计理念，构建高性能、可维护的数据库系统。通过完整的初始化数据和最新的模式生成系统，系统可以立即投入使用，无需额外的数据准备工作。