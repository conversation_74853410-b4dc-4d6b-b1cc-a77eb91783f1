"""
AWS服务模式生成系统测试套件

测试新的模式生成策略的正确性、性能和边界保护机制。
验证8种核心模式类型、特殊服务处理和数据库集成。
"""

import unittest
import re
import time
from typing import List, Dict, Any
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processors.regex_pattern_generator import (
    generate_comprehensive_service_patterns,
    batch_generate_all_patterns,
    validate_pattern_syntax,
    detect_pattern_conflicts,
    get_acronym,
    has_complex_suffix_support,
    has_special_variants,
    generate_boundary_protected_pattern
)


class TestPatternGeneration(unittest.TestCase):
    """模式生成核心功能测试"""
    
    def setUp(self):
        """测试初始化"""
        self.sample_ec2_service = {
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        }
        
        self.sample_rds_service = {
            'id': 2,
            'authoritative_full_name': 'Amazon Relational Database Service (RDS)',
            'full_name_en': 'Amazon Relational Database Service (RDS)',
            'short_name_en': 'Amazon RDS',
            'service_code': 'rds'
        }
        
        self.sample_aurora_service = {
            'id': 3,
            'authoritative_full_name': 'Amazon Aurora',
            'full_name_en': 'Amazon Aurora',
            'short_name_en': 'Aurora',
            'service_code': 'aurora'
        }
    
    def test_acronym_extraction(self):
        """测试缩写提取功能"""
        # 测试括号中的缩写
        self.assertEqual(get_acronym('Amazon Elastic Compute Cloud (EC2)'), 'EC2')
        self.assertEqual(get_acronym('Amazon Relational Database Service (RDS)'), 'RDS')
        
        # 测试无括号的情况
        self.assertEqual(get_acronym('Amazon Aurora'), 'AA')
        
        # 测试复杂情况
        self.assertEqual(get_acronym('AWS Identity and Access Management (IAM)'), 'IAM')
    
    def test_suffix_support_detection(self):
        """测试后缀支持检测"""
        self.assertTrue(has_complex_suffix_support(self.sample_ec2_service))
        self.assertTrue(has_complex_suffix_support(self.sample_rds_service))
        
        # 测试不支持后缀的服务
        lambda_service = {'service_code': 'lambda'}
        self.assertFalse(has_complex_suffix_support(lambda_service))
    
    def test_special_variants_detection(self):
        """测试特殊变体检测"""
        self.assertTrue(has_special_variants(self.sample_aurora_service))
        self.assertTrue(has_special_variants(self.sample_rds_service))
        
        # 测试无特殊变体的服务
        self.assertFalse(has_special_variants(self.sample_ec2_service))
    
    def test_boundary_protection_pattern(self):
        """测试边界保护模式生成"""
        pattern = generate_boundary_protected_pattern('EC2')
        
        # 验证模式包含必要的保护机制
        self.assertIn('(?<!arn:aws', pattern)
        self.assertIn('(?<!https?://', pattern)
        self.assertIn('(?<![:_-])', pattern)
        self.assertIn('(?![:_-])', pattern)
        self.assertIn('(?![^\\s]*\\.[a-z]{2,4})', pattern)
        
        # 测试模式语法正确性
        try:
            re.compile(pattern, re.IGNORECASE)
        except re.error:
            self.fail("Generated boundary protection pattern has invalid syntax")
    
    def test_comprehensive_pattern_generation(self):
        """测试完整模式生成"""
        patterns = generate_comprehensive_service_patterns(self.sample_ec2_service)
        
        # 验证生成的模式数量
        self.assertGreater(len(patterns), 5)  # 至少应该有基础模式
        
        # 验证模式类型
        pattern_names = [p['pattern_name'] for p in patterns]
        expected_patterns = [
            'EC2_FULL_COMPLEX_SUFFIX',
            'EC2_FULL_STANDARD',
            'EC2_SHORT_COMPLEX_SUFFIX',
            'EC2_SHORT_STANDARD',
            'EC2_ACRONYM_COMPLEX_SUFFIX',
            'EC2_ACRONYM_STANDARD'
        ]
        
        for expected in expected_patterns:
            self.assertIn(expected, pattern_names)
        
        # 验证优先级排序
        priorities = [p['priority'] for p in patterns]
        self.assertEqual(priorities, sorted(priorities, reverse=True))
    
    def test_pattern_syntax_validation(self):
        """测试模式语法验证"""
        # 有效模式
        valid_pattern = {
            'pattern_name': 'TEST_VALID',
            'regex_string': r'\bEC2\b'
        }
        is_valid, error = validate_pattern_syntax(valid_pattern)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # 无效模式
        invalid_pattern = {
            'pattern_name': 'TEST_INVALID',
            'regex_string': r'[invalid regex('
        }
        is_valid, error = validate_pattern_syntax(invalid_pattern)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_conflict_detection(self):
        """测试冲突检测"""
        patterns = [
            {'pattern_name': 'TEST1', 'priority': 100},
            {'pattern_name': 'TEST2', 'priority': 100},  # 优先级冲突
            {'pattern_name': 'TEST3', 'priority': 110}
        ]
        
        conflicts = detect_pattern_conflicts(patterns)
        self.assertEqual(len(conflicts), 1)
        self.assertEqual(conflicts[0]['type'], 'priority_conflict')
        self.assertEqual(conflicts[0]['priority'], 100)
    
    def test_batch_generation(self):
        """测试批量生成"""
        services = [self.sample_ec2_service, self.sample_rds_service]
        all_patterns = batch_generate_all_patterns(services)
        
        # 验证生成的模式数量
        self.assertGreater(len(all_patterns), 10)
        
        # 验证每个模式都有必要的字段
        for pattern in all_patterns:
            self.assertIn('pattern_name', pattern)
            self.assertIn('pattern_type', pattern)
            self.assertIn('regex_string', pattern)
            self.assertIn('priority', pattern)
            self.assertIn('related_service_id', pattern)
            self.assertIn('service_code', pattern)


class TestPatternMatching(unittest.TestCase):
    """模式匹配功能测试"""
    
    def setUp(self):
        """测试初始化"""
        self.ec2_patterns = generate_comprehensive_service_patterns({
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        })
        
        # 创建正则表达式对象
        self.compiled_patterns = []
        for pattern in self.ec2_patterns:
            try:
                compiled = re.compile(pattern['regex_string'], re.IGNORECASE)
                self.compiled_patterns.append({
                    'name': pattern['pattern_name'],
                    'regex': compiled,
                    'priority': pattern['priority'],
                    'notes': pattern.get('notes', '')
                })
            except re.error as e:
                self.fail(f"Pattern {pattern['pattern_name']} has invalid syntax: {e}")
    
    def test_basic_service_matching(self):
        """测试基本服务名称匹配"""
        test_cases = [
            "Amazon Elastic Compute Cloud (EC2)",
            "Amazon EC2",
            "EC2",
            "Amazon EC2 instances",
            "EC2 P3 instances",
            "Amazon Elastic Compute Cloud (EC2) instance family"
        ]
        
        for test_text in test_cases:
            matched = False
            for pattern in self.compiled_patterns:
                if pattern['regex'].search(test_text):
                    matched = True
                    break
            self.assertTrue(matched, f"No pattern matched: {test_text}")
    
    def test_boundary_protection(self):
        """测试边界保护机制"""
        # 这些文本不应该被匹配
        protected_cases = [
            "arn:aws:ec2:us-east-1:123456789012:instance/i-1234567890abcdef0",
            "https://console.aws.amazon.com/ec2/",
            "my-ec2-instance",
            "ec2-user",
            "example.com/ec2"
        ]
        
        for test_text in protected_cases:
            matched_patterns = []
            for pattern in self.compiled_patterns:
                if pattern['regex'].search(test_text):
                    matched_patterns.append(pattern['name'])
            
            # 某些上下文保护模式可能会匹配，但标准模式不应该匹配
            standard_patterns = [p for p in matched_patterns if 'CONTEXT' not in p]
            self.assertEqual(len(standard_patterns), 0, 
                           f"Standard patterns incorrectly matched protected text '{test_text}': {standard_patterns}")
    
    def test_complex_suffix_matching(self):
        """测试复合后缀匹配"""
        test_cases = [
            ("Amazon EC2 P3 instances", "P3 instances"),
            ("EC2 C5 instance family", "C5 instance family"),
            ("Amazon Elastic Compute Cloud (EC2) instance type", "instance type")
        ]
        
        for test_text, expected_suffix in test_cases:
            matched = False
            for pattern in self.compiled_patterns:
                if 'COMPLEX_SUFFIX' in pattern['name']:
                    match = pattern['regex'].search(test_text)
                    if match and len(match.groups()) > 0:
                        captured_suffix = match.group(1)
                        if expected_suffix in captured_suffix:
                            matched = True
                            break
            
            self.assertTrue(matched, f"Complex suffix not properly captured for: {test_text}")
    
    def test_priority_ordering(self):
        """测试优先级排序"""
        # 验证模式按优先级正确排序
        priorities = [p['priority'] for p in self.compiled_patterns]
        self.assertEqual(priorities, sorted(priorities, reverse=True))
        
        # 测试高优先级模式优先匹配
        test_text = "Amazon Elastic Compute Cloud (EC2) P3 instances"
        
        matched_patterns = []
        for pattern in self.compiled_patterns:
            if pattern['regex'].search(test_text):
                matched_patterns.append((pattern['name'], pattern['priority']))
        
        # 应该有多个模式匹配，但最高优先级的应该是复合后缀模式
        self.assertGreater(len(matched_patterns), 1)
        highest_priority = max(matched_patterns, key=lambda x: x[1])
        self.assertIn('COMPLEX_SUFFIX', highest_priority[0])


class TestSpecialServices(unittest.TestCase):
    """特殊服务处理测试"""
    
    def test_aurora_patterns(self):
        """测试Aurora服务模式"""
        aurora_service = {
            'id': 3,
            'authoritative_full_name': 'Amazon Aurora',
            'full_name_en': 'Amazon Aurora',
            'short_name_en': 'Aurora',
            'service_code': 'aurora'
        }
        
        patterns = generate_comprehensive_service_patterns(aurora_service)
        pattern_names = [p['pattern_name'] for p in patterns]
        
        # 验证包含Aurora特殊变体
        self.assertIn('AURORA_POSTGRESQL_VARIANT', pattern_names)
        self.assertIn('AURORA_MYSQL_VARIANT', pattern_names)
        self.assertIn('AURORA_GENERAL', pattern_names)
    
    def test_rds_patterns(self):
        """测试RDS服务模式"""
        rds_service = {
            'id': 2,
            'authoritative_full_name': 'Amazon Relational Database Service (RDS)',
            'full_name_en': 'Amazon Relational Database Service (RDS)',
            'short_name_en': 'Amazon RDS',
            'service_code': 'rds'
        }
        
        patterns = generate_comprehensive_service_patterns(rds_service)
        pattern_names = [p['pattern_name'] for p in patterns]
        
        # 验证包含RDS引擎变体
        self.assertIn('RDS_FOR_POSTGRESQL', pattern_names)
        self.assertIn('RDS_FOR_MYSQL', pattern_names)
        self.assertIn('RDS_GENERAL', pattern_names)


class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def test_pattern_generation_performance(self):
        """测试模式生成性能"""
        # 创建大量服务数据
        services = []
        for i in range(100):
            services.append({
                'id': i,
                'authoritative_full_name': f'Test Service {i} (TS{i})',
                'full_name_en': f'Test Service {i} (TS{i})',
                'short_name_en': f'Test Service {i}',
                'service_code': f'ts{i}'
            })
        
        # 测试批量生成性能
        start_time = time.time()
        all_patterns = batch_generate_all_patterns(services)
        generation_time = time.time() - start_time
        
        # 验证性能指标
        self.assertLess(generation_time, 10.0, "Pattern generation took too long")
        self.assertGreater(len(all_patterns), 500, "Not enough patterns generated")
        
        print(f"Generated {len(all_patterns)} patterns in {generation_time:.2f} seconds")
        print(f"Average: {len(all_patterns)/generation_time:.1f} patterns/second")
    
    def test_pattern_matching_performance(self):
        """测试模式匹配性能"""
        # 生成EC2模式
        ec2_patterns = generate_comprehensive_service_patterns({
            'id': 1,
            'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            'full_name_en': 'Amazon Elastic Compute Cloud (EC2)',
            'short_name_en': 'Amazon EC2',
            'service_code': 'ec2'
        })
        
        # 编译正则表达式
        compiled_patterns = []
        for pattern in ec2_patterns:
            compiled_patterns.append(re.compile(pattern['regex_string'], re.IGNORECASE))
        
        # 创建测试文本
        test_texts = [
            "Amazon EC2 instances are great",
            "EC2 P3 instances for machine learning",
            "Amazon Elastic Compute Cloud (EC2) provides scalable computing",
            "arn:aws:ec2:us-east-1:123456789012:instance/i-1234567890abcdef0",
            "Visit https://console.aws.amazon.com/ec2/ for more info"
        ] * 100  # 重复100次
        
        # 测试匹配性能
        start_time = time.time()
        total_matches = 0
        
        for text in test_texts:
            for pattern in compiled_patterns:
                if pattern.search(text):
                    total_matches += 1
        
        matching_time = time.time() - start_time
        
        # 验证性能指标
        self.assertLess(matching_time, 5.0, "Pattern matching took too long")
        
        print(f"Processed {len(test_texts)} texts with {len(compiled_patterns)} patterns in {matching_time:.2f} seconds")
        print(f"Found {total_matches} matches")
        print(f"Average: {len(test_texts)/matching_time:.1f} texts/second")


def run_all_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestPatternGeneration,
        TestPatternMatching,
        TestSpecialServices,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print(f"\n{'='*60}")
    print(f"Test Results Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*60}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)