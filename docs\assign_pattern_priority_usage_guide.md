# assign_pattern_priority 函数使用指南

## 概述

`assign_pattern_priority` 是一个PostgreSQL存储函数，用于自动为正则表达式模式分配合适的优先级。该函数实现了分层优先级系统，确保模式匹配的准确性和一致性。

## 函数签名

```sql
CREATE OR REPLACE FUNCTION assign_pattern_priority(
    p_pattern_name VARCHAR(255),
    p_pattern_type regex_pattern_type,
    p_regex_string TEXT,
    p_related_service_id BIGINT,
    p_is_compound BOOLEAN DEFAULT FALSE,
    p_suffix_group TEXT DEFAULT NULL
) RETURNS TABLE(tier SMALLINT, value INT)
```

## 使用场景

### 1. AWS服务同步系统中的自动使用

**使用时机**: 当AWS服务同步系统发现新服务或更新现有服务时

**代码示例**:
```python
from processors.enhanced_pattern_generator import EnhancedPatternGenerator

# 初始化生成器
generator = EnhancedPatternGenerator(db_client)

# 为新服务生成模式（自动分配优先级）
service_info = {
    'id': 123,
    'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
    'base_name': 'Amazon Elastic Compute Cloud',
    'short_name_en': 'Amazon EC2',
    'service_code': 'ec2'
}

patterns = generator.generate_patterns_for_new_service(service_info)
stored_count = generator.store_patterns_to_database(patterns)
```

**优势**: 
- 自动化处理，无需手动干预
- 确保新服务的模式优先级符合系统规范
- 减少人为错误

### 2. 手动添加新正则模式

**使用时机**: 管理员通过管理界面或SQL手动添加特殊的正则模式

**Python代码示例**:
```python
from processors.priority_assignment_helper import PriorityAssignmentHelper

helper = PriorityAssignmentHelper(db_client)

# 分配优先级
priority_info = helper.assign_single_pattern_priority(
    pattern_name='LAMBDA_CUSTOM_PATTERN',
    pattern_type='SERVICE_NAME',
    regex_string=r'AWS\s+Lambda\s+function',
    related_service_id=456,
    is_compound=True,
    suffix_group='function'
)

print(f"分配的优先级: 层级{priority_info['priority_tier']}, 值{priority_info['priority_value']}")
```

**直接SQL示例**:
```sql
-- 插入新模式时自动分配优先级
INSERT INTO regex_patterns (
    pattern_name, pattern_type, regex_string, related_service_id,
    is_compound_pattern, compound_suffix_group,
    priority_tier, priority_value, is_active
)
SELECT 
    'RDS_MYSQL_COMPOUND',
    'SERVICE_NAME',
    'Amazon\s+RDS\s+for\s+MySQL',
    (SELECT id FROM service_names WHERE service_code = 'rds'),
    TRUE,
    'mysql',
    tier,
    value,
    TRUE
FROM assign_pattern_priority(
    'RDS_MYSQL_COMPOUND',
    'SERVICE_NAME',
    'Amazon\s+RDS\s+for\s+MySQL',
    (SELECT id FROM service_names WHERE service_code = 'rds'),
    TRUE,
    'mysql'
);
```

### 3. 批量优先级重新计算

**使用时机**: 系统维护、优先级规则更新、数据清理

**Python批量处理**:
```python
# 批量更新特定服务的所有模式
result = generator.update_existing_pattern_priorities(service_id=123)
print(f"更新了 {result['updated_count']} 个模式")

# 批量处理多个模式
patterns = [
    {
        'pattern_name': 'S3_FULL_STANDARD',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': r'Amazon\s+Simple\s+Storage\s+Service',
        'related_service_id': 789,
        'is_compound': False
    },
    # ... 更多模式
]

updated_patterns = helper.assign_batch_pattern_priorities(patterns)
```

**SQL批量更新**:
```sql
-- 批量更新现有模式的优先级
UPDATE regex_patterns 
SET (priority_tier, priority_value) = (
    SELECT tier, value 
    FROM assign_pattern_priority(
        pattern_name,
        pattern_type,
        regex_string,
        related_service_id,
        is_compound_pattern,
        compound_suffix_group
    )
)
WHERE is_active = TRUE 
AND related_service_id = (SELECT id FROM service_names WHERE service_code = 'ec2');
```

### 4. 优先级验证和审计

**使用时机**: 定期检查优先级分配是否正确，发现潜在问题

**验证查询**:
```sql
-- 查询优先级分配状态
SELECT 
    pattern_name,
    current_tier.priority_tier as current_tier,
    current_tier.priority_value as current_value,
    new_priority.tier as recommended_tier,
    new_priority.value as recommended_value,
    CASE 
        WHEN current_tier.priority_tier != new_priority.tier 
          OR current_tier.priority_value != new_priority.value 
        THEN 'NEEDS_UPDATE'
        ELSE 'UP_TO_DATE'
    END as status
FROM regex_patterns current_tier
CROSS JOIN LATERAL assign_pattern_priority(
    current_tier.pattern_name,
    current_tier.pattern_type,
    current_tier.regex_string,
    current_tier.related_service_id,
    current_tier.is_compound_pattern,
    current_tier.compound_suffix_group
) as new_priority
WHERE current_tier.is_active = TRUE;
```

**Python验证**:
```python
# 验证特定模式的优先级
is_valid = helper.validate_priority_assignment(
    pattern_name='EC2_FULL_STANDARD',
    expected_tier=3,
    expected_value=7
)

if not is_valid:
    print("优先级分配不符合预期，需要检查")
```

## 最佳实践

### 1. 集成到现有工作流

- **在模式生成时**: 始终调用函数分配优先级，而不是使用硬编码值
- **在数据迁移时**: 使用函数重新计算所有现有模式的优先级
- **在系统维护时**: 定期运行验证查询，确保优先级一致性

### 2. 错误处理

```python
try:
    priority_info = helper.assign_single_pattern_priority(...)
except Exception as e:
    # 使用默认优先级
    priority_info = {'priority_tier': 8, 'priority_value': 5}
    logger.warning(f"优先级分配失败，使用默认值: {e}")
```

### 3. 性能优化

- **批量操作**: 对于大量模式，使用批量处理而不是逐个调用
- **事务管理**: 在事务中执行批量更新，确保数据一致性
- **索引优化**: 确保相关字段有适当的索引

### 4. 监控和日志

```python
# 记录优先级分配的详细信息
self.logger.info("Priority assigned", {
    "pattern_name": pattern_name,
    "priority_tier": tier,
    "priority_value": value,
    "computed_priority": (11 - tier) * 10 + value,
    "is_compound": is_compound
})
```

## 常见问题

### Q: 什么时候需要手动调用这个函数？

A: 主要在以下情况：
1. 添加新的正则模式时
2. 修改现有模式的属性时
3. 系统维护和数据清理时
4. 优先级规则更新后的重新计算

### Q: 函数返回的优先级如何使用？

A: 函数返回 `priority_tier` 和 `priority_value` 两个值：
- 存储到数据库的对应字段
- 计算最终优先级：`(11 - priority_tier) * 10 + priority_value`
- 用于 `ORDER BY` 查询中的排序

### Q: 如何处理函数调用失败的情况？

A: 建议使用默认优先级值（tier=8, value=5）并记录警告日志，确保系统继续运行。

### Q: 是否可以在代码中实现相同的逻辑？

A: 可以，但不推荐。数据库函数的优势：
- 确保逻辑一致性
- 更好的性能（减少网络往返）
- 集中管理优先级规则
- 支持直接SQL操作

## 相关文件

- `lambda/dev/aws_service_sync/processors/priority_assignment_helper.py` - Python集成接口
- `lambda/dev/aws_service_sync/processors/enhanced_pattern_generator.py` - 增强版模式生成器
- `lambda/dev/aws_service_sync/examples/priority_assignment_examples.py` - 使用示例
- `database/functions/assign_pattern_priority.sql` - 函数定义
