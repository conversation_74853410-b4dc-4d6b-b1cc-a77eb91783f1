"""
增强版正则模式生成器 - 集成优先级自动分配功能

该模块展示了如何在现有的正则模式生成器中集成 assign_pattern_priority 数据库函数，
实现自动化的优先级分配和管理。
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from storage.rds_client import RDSClient
from processors.priority_assignment_helper import PriorityAssignmentHelper
from utils.structured_logger import StructuredLogger

logger = logging.getLogger(__name__)


class EnhancedPatternGenerator:
    """
    增强版正则模式生成器
    
    集成了优先级自动分配功能，能够在生成正则模式时
    自动调用数据库函数分配合适的优先级。
    """
    
    def __init__(self, db_client: RDSClient):
        """
        初始化增强版模式生成器
        
        Args:
            db_client: 数据库客户端实例
        """
        self.db_client = db_client
        self.priority_helper = PriorityAssignmentHelper(db_client)
        self.logger = StructuredLogger("enhanced_pattern_generator")
    
    def generate_patterns_for_new_service(self, service_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        为新服务生成完整的正则模式集合，并自动分配优先级
        
        Args:
            service_info: 服务信息字典，包含：
                - id: 服务ID
                - authoritative_full_name: 权威全称
                - base_name: 基础名称
                - short_name_en: 英文简称
                - service_code: 服务代码
                
        Returns:
            包含优先级信息的正则模式列表
            
        Example:
            >>> service = {
            ...     'id': 123,
            ...     'authoritative_full_name': 'Amazon Elastic Compute Cloud (EC2)',
            ...     'base_name': 'Amazon Elastic Compute Cloud',
            ...     'short_name_en': 'Amazon EC2',
            ...     'service_code': 'ec2'
            ... }
            >>> patterns = generator.generate_patterns_for_new_service(service)
        """
        try:
            self.logger.info("Generating patterns for new service", {
                "service_id": service_info['id'],
                "service_name": service_info['authoritative_full_name']
            })
            
            # 生成基础模式
            base_patterns = self._create_base_patterns(service_info)
            
            # 为每个模式分配优先级
            patterns_with_priority = self.priority_helper.assign_batch_pattern_priorities(base_patterns)
            
            # 验证和后处理
            validated_patterns = self._validate_and_enhance_patterns(patterns_with_priority)
            
            self.logger.info("Successfully generated patterns with priorities", {
                "service_id": service_info['id'],
                "total_patterns": len(validated_patterns)
            })
            
            return validated_patterns
            
        except Exception as e:
            self.logger.error("Failed to generate patterns for service", {
                "service_id": service_info.get('id'),
                "error": str(e)
            })
            raise
    
    def _create_base_patterns(self, service_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        创建基础正则模式（不包含优先级）
        
        Args:
            service_info: 服务信息
            
        Returns:
            基础模式列表
        """
        patterns = []
        service_code = service_info['service_code'].upper()
        service_id = service_info['id']
        full_name = service_info['authoritative_full_name']
        base_name = service_info['base_name']
        short_name = service_info['short_name_en']
        
        # 1. 全称标准模式
        patterns.append({
            'pattern_name': f"{service_code}_FULL_STANDARD",
            'pattern_type': 'SERVICE_NAME',
            'regex_string': self._escape_for_regex(full_name),
            'related_service_id': service_id,
            'is_compound': False,
            'description': '标准全称模式'
        })
        
        # 2. 全称复合模式（如果适用）
        if self._has_common_suffixes(service_code):
            compound_regex = self._create_compound_regex(full_name)
            patterns.append({
                'pattern_name': f"{service_code}_FULL_COMPOUND",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': compound_regex,
                'related_service_id': service_id,
                'is_compound': True,
                'suffix_group': self._get_suffix_group(service_code),
                'description': '全称复合模式（带后缀）'
            })
        
        # 3. 简称标准模式
        if short_name and short_name != full_name:
            patterns.append({
                'pattern_name': f"{service_code}_SHORT_STANDARD",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': self._escape_for_regex(short_name),
                'related_service_id': service_id,
                'is_compound': False,
                'description': '标准简称模式'
            })
            
            # 4. 简称复合模式
            if self._has_common_suffixes(service_code):
                compound_regex = self._create_compound_regex(short_name)
                patterns.append({
                    'pattern_name': f"{service_code}_SHORT_COMPOUND",
                    'pattern_type': 'SERVICE_NAME',
                    'regex_string': compound_regex,
                    'related_service_id': service_id,
                    'is_compound': True,
                    'suffix_group': self._get_suffix_group(service_code),
                    'description': '简称复合模式（带后缀）'
                })
        
        # 5. 缩写模式（从括号中提取）
        acronym = self._extract_acronym(full_name)
        if acronym:
            patterns.append({
                'pattern_name': f"{service_code}_ABBREV_STANDARD",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"(?<![:_-])\\b{acronym}\\b(?![:_-])",
                'related_service_id': service_id,
                'is_compound': False,
                'description': '标准缩写模式'
            })
            
            # 6. 缩写复合模式
            if self._has_common_suffixes(service_code):
                patterns.append({
                    'pattern_name': f"{service_code}_ABBREV_COMPOUND",
                    'pattern_type': 'SERVICE_NAME',
                    'regex_string': f"(?<![:_-])\\b{acronym}\\s+({self._get_suffix_pattern(service_code)})",
                    'related_service_id': service_id,
                    'is_compound': True,
                    'suffix_group': self._get_suffix_group(service_code),
                    'description': '缩写复合模式（带后缀）'
                })
        
        # 7. 品牌变体模式（AWS前缀）
        if full_name.startswith('Amazon'):
            aws_variant = full_name.replace('Amazon', 'AWS', 1)
            patterns.append({
                'pattern_name': f"{service_code}_AWS_VARIANT",
                'pattern_type': 'SERVICE_NAME',
                'regex_string': self._escape_for_regex(aws_variant),
                'related_service_id': service_id,
                'is_compound': False,
                'description': 'AWS品牌变体模式'
            })
        
        return patterns
    
    def store_patterns_to_database(self, patterns: List[Dict[str, Any]]) -> int:
        """
        将带有优先级的模式存储到数据库
        
        Args:
            patterns: 包含优先级信息的模式列表
            
        Returns:
            成功存储的模式数量
        """
        try:
            stored_count = 0
            
            for pattern in patterns:
                try:
                    # 构建插入SQL
                    insert_sql = """
                    INSERT INTO regex_patterns (
                        pattern_name, pattern_type, regex_string, related_service_id,
                        is_compound_pattern, compound_suffix_group,
                        priority_tier, priority_value, is_active, notes, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (pattern_name) DO UPDATE SET
                        regex_string = EXCLUDED.regex_string,
                        priority_tier = EXCLUDED.priority_tier,
                        priority_value = EXCLUDED.priority_value,
                        updated_at = NOW()
                    """
                    
                    # 准备参数
                    params = (
                        pattern['pattern_name'],
                        pattern['pattern_type'],
                        pattern['regex_string'],
                        pattern['related_service_id'],
                        pattern.get('is_compound', False),
                        pattern.get('suffix_group'),
                        pattern['priority_tier'],
                        pattern['priority_value'],
                        True,  # is_active
                        f'{{"description": "{pattern.get("description", "")}"}}',
                        datetime.now()
                    )
                    
                    # 执行插入
                    self.db_client.execute_update(insert_sql, params)
                    stored_count += 1
                    
                    self.logger.debug("Stored pattern to database", {
                        "pattern_name": pattern['pattern_name'],
                        "priority_tier": pattern['priority_tier'],
                        "priority_value": pattern['priority_value']
                    })
                    
                except Exception as e:
                    self.logger.error("Failed to store pattern", {
                        "pattern_name": pattern.get('pattern_name', 'unknown'),
                        "error": str(e)
                    })
            
            self.logger.info("Completed storing patterns to database", {
                "total_patterns": len(patterns),
                "stored_count": stored_count
            })
            
            return stored_count
            
        except Exception as e:
            self.logger.error("Failed to store patterns to database", {
                "error": str(e)
            })
            raise
    
    def update_existing_pattern_priorities(self, service_id: Optional[int] = None) -> Dict[str, Any]:
        """
        更新现有模式的优先级
        
        Args:
            service_id: 可选的服务ID，如果提供则只更新该服务的模式
            
        Returns:
            更新结果统计
        """
        try:
            # 查询需要更新的模式
            query = """
            SELECT id, pattern_name, pattern_type, regex_string, related_service_id,
                   is_compound_pattern, compound_suffix_group,
                   priority_tier as current_tier, priority_value as current_value
            FROM regex_patterns 
            WHERE is_active = TRUE
            """
            
            params = []
            if service_id:
                query += " AND related_service_id = %s"
                params.append(service_id)
            
            existing_patterns = self.db_client.execute_query(query, params)
            
            updated_count = 0
            unchanged_count = 0
            
            for pattern_row in existing_patterns:
                # 重新计算优先级
                new_priority = self.priority_helper.assign_single_pattern_priority(
                    pattern_name=pattern_row[1],
                    pattern_type=pattern_row[2],
                    regex_string=pattern_row[3],
                    related_service_id=pattern_row[4],
                    is_compound=pattern_row[5],
                    suffix_group=pattern_row[6]
                )
                
                # 检查是否需要更新
                if (pattern_row[7] != new_priority['priority_tier'] or 
                    pattern_row[8] != new_priority['priority_value']):
                    
                    # 更新数据库
                    update_sql = """
                    UPDATE regex_patterns 
                    SET priority_tier = %s, priority_value = %s, updated_at = NOW()
                    WHERE id = %s
                    """
                    
                    self.db_client.execute_update(update_sql, (
                        new_priority['priority_tier'],
                        new_priority['priority_value'],
                        pattern_row[0]
                    ))
                    
                    updated_count += 1
                    
                    self.logger.debug("Updated pattern priority", {
                        "pattern_name": pattern_row[1],
                        "old_tier": pattern_row[7],
                        "old_value": pattern_row[8],
                        "new_tier": new_priority['priority_tier'],
                        "new_value": new_priority['priority_value']
                    })
                else:
                    unchanged_count += 1
            
            result = {
                'total_patterns': len(existing_patterns),
                'updated_count': updated_count,
                'unchanged_count': unchanged_count,
                'update_rate': updated_count / len(existing_patterns) if existing_patterns else 0
            }
            
            self.logger.info("Completed priority update", result)
            
            return result
            
        except Exception as e:
            self.logger.error("Failed to update pattern priorities", {
                "error": str(e)
            })
            raise
    
    # 辅助方法
    def _escape_for_regex(self, text: str) -> str:
        """转义正则表达式特殊字符"""
        import re
        return re.escape(text).replace(r'\s+', r'\s+')
    
    def _extract_acronym(self, full_name: str) -> Optional[str]:
        """从全称中提取缩写"""
        import re
        match = re.search(r'\(([A-Z0-9]+)\)', full_name)
        return match.group(1) if match else None
    
    def _has_common_suffixes(self, service_code: str) -> bool:
        """检查服务是否有常见后缀"""
        suffix_services = ['EC2', 'EBS', 'RDS', 'S3', 'IAM', 'LAMBDA']
        return service_code in suffix_services
    
    def _get_suffix_group(self, service_code: str) -> str:
        """获取服务的后缀组"""
        suffix_map = {
            'EC2': 'instance',
            'EBS': 'volume',
            'RDS': 'database',
            'S3': 'bucket',
            'IAM': 'policy',
            'LAMBDA': 'function'
        }
        return suffix_map.get(service_code, 'general')
    
    def _get_suffix_pattern(self, service_code: str) -> str:
        """获取服务的后缀正则模式"""
        suffix_patterns = {
            'EC2': 'instance|instances|instance\\s+family|instance\\s+type',
            'EBS': 'volume|volumes|volume\\s+type',
            'RDS': 'database|databases|engine|cluster',
            'S3': 'bucket|buckets|object|objects',
            'IAM': 'policy|policies|role|roles|user|users',
            'LAMBDA': 'function|functions|layer|layers'
        }
        return suffix_patterns.get(service_code, 'service|services')
    
    def _create_compound_regex(self, base_name: str) -> str:
        """创建复合正则表达式"""
        escaped_name = self._escape_for_regex(base_name)
        return f"{escaped_name}\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|volume|volumes|service|services))"
    
    def _validate_and_enhance_patterns(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证和增强模式"""
        # 这里可以添加额外的验证逻辑
        return patterns
